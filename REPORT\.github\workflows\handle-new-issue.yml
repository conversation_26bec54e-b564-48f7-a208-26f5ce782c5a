name: 'Handle new issue'
on:
  issues:
    types: [opened]

jobs:
  handle_new_issue:
    runs-on: ubuntu-latest
    name: Handle new issue
    steps:
      - name: Greet & Apply label
        uses: themeselection/gh-action-comment-on-new-issue@v4.0.1
        with:
          message: |
            ### Hi there <img src="https://raw.githubusercontent.com/MartinHeinz/MartinHeinz/master/wave.gif" width="30px">
            Thanks a lot for getting in touch with us. Your support has been raised, our response time could be 1 - 2 business days.
            In the meantime, feel free to check out related issues in the repository for more help. We really appreciate your patience.
          raise-support-using-form-msg: |
            Thanks for creating an issue ☺️
            It seems our [raise support](https://themeselection.com/tools/github/raise-support) form was skipped while creating this issue.
            Please consider raising support via our support form only which will create issues using essential information we need to resolve your query quickly.
            This will help in growing an excellent community where,
            - Other people can get it resolved if they have the same query
            - We can easily understand your query
            - Community at a later stage can also provide you solutions/suggestions
            Closing this issue.
            Regards.
      - name: Find duplicates
        uses: wow-actions/potential-duplicates@v1.0.8
        with:
          GITHUB_TOKEN: ${{ github.token }}
          label: duplicate
          comment: >
            Potential duplicates: {{#issues}}
              - #{{ number }} _({{ accuracy }}% Match)_
            {{/issues}}
