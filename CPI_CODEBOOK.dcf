﻿[Dictionary]
Version=CSPro 7.7
Label=CPI_CODEBOOK
Name=CPI_CODEBOOK_DICT
RecordTypeStart=1
RecordTypeLen=1
Positions=Relative
ZeroFill=No
DecimalChar=Yes
SecurityOptions=14B16533C06A03D8BB94E13DC250B87AD6C97C051DB9519347665CA1CBC11C68

[Level]
Label=CPI_CODEBOOK Level
Name=CPI_CODEBOOK_LEVEL

[IdItems]

[Item]
Label=Outlet_uuid
Name=OUTLET_UUID
Start=2
Len=12
DataType=Alpha

[Item]
Label=R_type
Name=R_TYPE
Start=14
Len=1
DataType=Numeric

[ValueSet]
Label=R_type
Name=R_TYPE_VS1
Value=1;Urban
Value=2;Rural

[Item]
Label=ENUMERATION MARKET (EM)
Name=ENUMERATION_MARKET_EM
Start=15
Len=2
DataType=Numeric

[ValueSet]
Label=ENUMERATION MARKET (EM)
Name=ENUMERATION_MARKET_EM_VS1
Value=1;KWINELLA
Value=2;WILLINGARA BA
Value=3;JARENG
Value=4;BRIKAMA BA
Value=5;SARE BOJO
Value=6;FATOTO
Value=7;SARE NGAI
Value=8;WASSU
Value=9;KAUR
Value=10;FARAFENNI
Value=11;KERR PATEH
Value=12;NDUNGU KEBBEH
Value=13;BARRA
Value=14;BANJUL
Value=15;BAKAU
Value=16;SEREKUNDA
Value=17;LATRIKUNDA
Value=18;LAMIN
Value=19;BRIKAMA
Value=20;GUNJUR
Value=21;SIBANOR
Value=22;KALAGI
Value=23;SOMA
Value=24;BANSANG
Value=25;KEREWAN
Value=26;KUNTAUR
Value=27; Basse Santo-su
Value=28;FASS NJAGA CHOI

[Record]
Label=CPI_CODEBOOK_COVER Record
Name=CPI_CODEBOOK_REC
RecordTypeValue='1'
RecordLen=69

[Item]
Label=LGA
Name=LGA
Start=17
Len=1
DataType=Numeric

[ValueSet]
Label=LGA
Name=LGA_VS1
Value=1;Banjul
Value=2;Kanifing
Value=3;Brikama
Value=4;Mansakonko
Value=5;Kerewan
Value=6;Kuntaur
Value=7;Janjanbureh
Value=8;Basse

[Item]
Label=DISTRICT CODE
Name=DISTRICT_CODE
Start=18
Len=2
DataType=Numeric

[ValueSet]
Label=DISTRICT CODE
Name=DISTRICT_CODE_VS1
Value=1;KIANG CENTRAL
Value=2;JARRA CENTRAL
Value=3;NIAMINA EAST
Value=4; LOWE FULLADOU WEST
Value=5;JIMARA
Value=6;KANTORA
Value=7;WULI WEST
Value=8;NIANI
Value=9;LOWER SALOUNM
Value=10;UPPER BADIBOU
Value=11;LOWER NUIMI
Value=12;LOWER NUIMI
Value=13;LOWER NUIMI
Value=14;BCC
Value=15;KMC
Value=16;KMC
Value=17;KMC
Value=18;WCR
Value=19;WCR
Value=20;WCR
Value=21;FONI BINTANG
Value=22;FONI JARROL
Value=23;JARRA WEST
Value=24; Upper FULLADOU WEST
Value=25;LOWER BADIBOU
Value=26;NIANI
Value=27;BASSE
Value=28;LOWER NUIMI

[Item]
Label=District _Name
Name=DISTRICT__NAME
Start=20
Len=45
DataType=Alpha

[Item]
Label=SETTLEMENT
Name=SETTLEMENT
Start=65
Len=3
DataType=Numeric

[ValueSet]
Label=SETTLEMENT
Name=SETTLEMENT_VS1
Value=1;KWINELLA
Value=2;WILLINGARA BA
Value=3;JARENG
Value=4;BRIKAMA BA
Value=5;SARE BOJO
Value=6;FATOTO
Value=7;SARE NGAI
Value=8;WASSU
Value=9;KAUR
Value=10;FARAFENNI
Value=11;KERR PATEH
Value=12;NDUNGU KEBBEH
Value=13;BARRA
Value=14;BANJUL
Value=15;BAKAU
Value=16;SEREKUNDA
Value=17;LATRIKUNDA
Value=18;LAMIN
Value=19;BRIKAMA
Value=20;GUNJUR
Value=21;SIBANOR
Value=22;KALAGI
Value=23;SOMA
Value=24;BANSANG
Value=25;KEREWAN
Value=26;KUNTAUR
Value=27;BASSE
Value=28;FASS NJAGA CHOI

[Item]
Label=INTRODUCTION
Name=INTRODUCTION
Start=68
Len=1
DataType=Numeric

[ValueSet]
Label=INTRODUCTION
Name=INTRODUCTION_VS1
Value=1;Yes
Value=2;No

[Item]
Label=Interview Result
Name=INTERVIEW_RESULT
Start=69
Len=1
DataType=Numeric

[ValueSet]
Label=Interview Result
Name=INTERVIEW_RESULT_VS1
Value=1;Completed

[ValueSet]
Label=Interview Results
Name=INTERVIEW_RESULT_VS2
Value=2;Partially Completed
Value=3;Unavialable

[Record]
Label=SECTION A. IDENTIFICATION OF OUTLET
Name=SECTION_A_IDENTIFICATION_OF_OUTLET1
RecordTypeValue='4'
MaxRecords=100
RecordLen=52

[Item]
Label=Item_ID
Name=ITEM_ID
Start=17
Len=3
DataType=Numeric

[Item]
Label=1.7 NAME OF ITEM
Name=NAME_OF_ITEM
Start=20
Len=4
DataType=Numeric

[ValueSet]
Label=1.7 NAME OF ITEM
Name=NAME_OF_ITEM_VS1
Value=1;Long-Grained Rice (Imported)
Value=2;Paddy Rice Long Grain (Local)
Value=3;Medium-Grained Rice (Imported)
Value=4;Small grained rice (imported)
Value=5;Basmati Rice (Imported)
Value=6;Maize
Value=7;Millet
Value=8;Bread
Value=9;Findi
Value=10;Millet Flour
Value=11;Spaghetti
Value=12;Fish Pie
Value=13;Cake (pan, etc.)
Value=14;Gari (Cassava Flour)
Value=15;Beef
Value=16;Sheep meat (mutton)
Value=17;Chicken Legs (Imported)
Value=18;Chicken (Local)
Value=19;Pork
Value=20;Canned meat (200 Gram)
Value=21;Goat Meat
Value=22;Fresh Bonga
Value=23;Smoked Bonga
Value=24;Cat Fish
Value=25;Fresh Grouper/Lady Fish
Value=26;Fresh Barracuda
Value=27;Dried Couta/Tenny
Value=28;Shrimps
Value=29;Tilapia
Value=30;Sardine (Tin Fish 125g)
Value=31;Dried fish
Value=32;Eggs
Value=33;Fresh Milk
Value=34;Sour Milk
Value=35;Evaporated Milk (Peak)
Value=36;Powdered Milk(400g)
Value=37;Milk Sacket 25g (e.g. Vitalait,sophie,peak)
Value=38;Yoghurt(125g)
Value=39;Palm Oil
Value=40;Butter (250Gram)
Value=41;Vegetable Oil (Sold loose)
Value=42;Peanut Buter
Value=43;Margarine (500 Grams)
Value=44;Groundnuts-Shelled
Value=45;Raw Groundnut Powder
Value=46;Banana
Value=47;Oranges
Value=48;Mangoes
Value=49;Lime
Value=50;Apple
Value=51;Baobab fruit
Value=52;Paw - Paw
Value=53;Water Melon
Value=54;Roasted Groundnut
Value=55;Dates
Value=56;Potatoes (Irish)
Value=57;Sweet Potatoes
Value=58;Cassava
Value=59;Dry Beans
Value=60;Small Pepper-Fresh
Value=61;Tomatoes-Fresh
Value=62;Bitter Tomato
Value=63;Garden Eggs
Value=64;Okra
Value=65;Onion
Value=66;Pumpkin
Value=67;Big Red Pepper
Value=68;Kren-Kren
Value=69;Bisap
Value=70;Cabbage
Value=71;Lettuce (Salad)
Value=72;Tomato Puree (Paste,70g)
Value=73;Carrot
Value=74;Cucumber
Value=75;Cassava Leaves
Value=76;Sugar
Value=77;Mint Fresh
Value=78;Chewing gum (Hollywood)
Value=79;Honey
Value=80;Ice Cream (Bowl 1 Litre)
Value=81;Mint Stick
Value=82;Biscuit (E.g.Bidew)
Value=83;Salt
Value=84;Garlic
Value=85;Maggi Cube
Value=86;Small Dry Pepper
Value=87;Locust Beans (Neteetu)
Value=88;Black Pepper (Whole Seed)
Value=89;Vinegar
Value=90;Chilli powder
Value=91;Mayonnaise
Value=92;Tea Bags(200g)
Value=93;Coffee-Nescafe (50gr)
Value=94;Powdered Tea (Ovaltine, 200gr)
Value=95;Juices (Wonjo)
Value=96;Chinese Green Tea (25g) (Ataya)
Value=97;Soft Drinks (Coke, Spirite, Fanta)
Value=98;ineral Water (Malta, Cocktail,Vimto, 330m
Value=99;Cold water (1.5Ltr)
Value=100;Spirits
Value=101;Wines (Red, White, etc.)
Value=102;Palm Wine
Value=103;Beer (lager and porter)
Value=104;Stout(330ml)
Value=105;Piccadilly
Value=106;Marlboro
Value=107;Benson
Value=108;anise (Tobacco) Wrapped in Paper 50Gra
Value=109;Bond
Value=110;Snuff
Value=111;Rizzla
Value=112;Kola Nuts
Value=113;Bitter Cola Nut
Value=114;Material for trouser
Value=115;Babies' Clothes (Napkin)
Value=116;Boy's Underpants (4-6 Years)
Value=117;Boy's Shirt (12-16 Years)
Value=118;Boy's Trousers (12-16 Years)
Value=119;Ladies Docket
Value=120;Ladies Dress
Value=121;Ladies Underwear
Value=122;Lady's Blouse
Value=123;Men's Shirts
Value=124;Men's Trousers
Value=125;Men's Underwear
Value=126;Singlet
Value=127;Track Suits
Value=128;T-Shirt
Value=129;Girl's Dress (10 Years)
Value=130;Brassiere
Value=131;Men's Sock
Value=132;School Uniform (Grade 1)
Value=133;Laundry (Hand Wash, Trousers and Shirt for Men)
Value=134;Boy's Rubber Sandals (12-16 Years)
Value=135;Boy's Shoes-Plastic (Nyambalastic (12-16 Years)
Value=136;Boy's Slippers (12-16 Years)
Value=137;Boy's Sports Shoes (12-16 Years)
Value=138;Girls's Slippers (Charack, 12-16 Years)
Value=139;Girl's Rubber Sandals (12-16 Years)
Value=140;Girls' Full Shoes-Synthetic (12-16 Years)
Value=141;Men's Full Shoe
Value=142;Men's Slipper
Value=143;Men's Sports Shoes
Value=144;Women's Full Shoes
Value=145;Women's Slippers
Value=146;One Bedroom Sitter (Single Room)
Value=147;One Bedroom (Room & Parlour)
Value=148;Two Bedroom Without Toilet & Kitchen Facility
Value=149;Facility
Value=150;Three Bedroom Without Facility
Value=151;Three Bedroom With Facility
Value=152;Cement
Value=153;Ceramic Tiles (Indian)
Value=154;Joints (Three-Quarter T-Joint)
Value=155;Oil Paints (3.6)
Value=156;Water Paints (18 Ltr)
Value=157;Electric Pipes (20 mm, White)
Value=158;Plastering of Room (3 by 3 meter room size)
Value=159;Tilling of Room (3 by 3 meter room size)
Value=160;Painting of Room (3 by 3 meter room size)
Value=161;Window Frame Set (Aluminium, Standard Window)
Value=162;Water Charges (10 cum)
Value=163;Electricity (300 kwh)
Value=164;Gas
Value=165;Charcoal
Value=166;Firewood
Value=167;Wooden Bed (Box Bed With Decorated Head)
Value=168;Bedstead
Value=169;Sofa Chair (One Set)
Value=170;Mattress (Double Bed 8 Inches)
Value=171;Vynil Floor Cover (Thick, 1 Mtr)
Value=172;Ready-made Curtains (1 Packet)
Value=173;Mosquito Net
Value=174;Mat (sleeping/Praying)
Value=175;Bed Sheet (Double Bed)
Value=176;Pedestal Fan
Value=177;Freezer (Upright)
Value=178;Generator
Value=179;Iron (charcoal)
Value=180;Solar panel (With Inverter)
Value=181;Refrigerator With Freezer (165-190 Ltr)
Value=182;Cooking Knife
Value=183;Frying Pan
Value=184;Negro Pot
Value=185;Thermos Flask
Value=186;Bucket (Plastic)
Value=187;Plastic Basin
Value=188;Plastic Pan
Value=189;Batteries
Value=190;Torch Light
Value=191;Wheel Barrow
Value=192;Bleach (Ordsavel)
Value=193;Candle
Value=194;Insecticides Sprays (400 mle)
Value=195;Lighters
Value=196;Matches
Value=197;Soda Powder
Value=198;Soda Soap
Value=199;Starch (Dakandeh)
Value=200;Incense (Stick, 1 Packet)
Value=201;Light Bulb (Energy Bulb)
Value=202;Maid servants-Full Time
Value=203;Drivers
Value=204;Amoxilliyin
Value=205;Aspirin
Value=206;Coartem (Adult 1 Doze)
Value=207;Ibrufen (10 Tablets)
Value=208;Paracetamol (1 strip of 10 tablets)
Value=209;Out-Patient Fees (Ticket Paid)
Value=210;Doctor's Fee (Public Health Facility)
Value=211;Doctor's Fee (Private Clinic)
Value=212;Dental Costs (Tooth Uproot)
Value=213;Hospital Fees (Hospitalization, Public)
Value=214;Car (Toyota Yaris)
Value=215;Motorcycle (fellenco/safari, 125cc)
Value=216;Purchase of Bicycle
Value=217;Bicycle Tyre
Value=218;Motor vehicle Tyre Tube (Inner)
Value=219;Puncture Repair
Value=220;Servicing of Motor Vehicle (Brake Repair, Labour)
Value=221;Bicycle Tyre
Value=222;Car Battery
Value=223;Diesel
Value=224;Motor Oil (4.5 Ltr)
Value=225;Petrol
Value=226;Bus Fares (Seven Kilometers)
Value=227;Taxi Fares (Shared Taxi, Fixed Route)
Value=228;School Bus Fare (Single Ticket)
Value=229;International Flights (Return Ticket, Six Hours)
Value=230;Ferry Fares (Big Ferry, Single Ticket)
Value=231;Letter Postage (International)
Value=232;Letter Postage (Local)
Value=233;Fixed line Telephone Set (one Piece)
Value=234;Mobile phone (Itel)
Value=235;Internet Costs (1 Hrs, Cyber Café)
Value=236;E-Credit
Value=237;Memory Card Chip (4 GB)
Value=238;Television Set (LG, LED, 32, SAMSUMG)
Value=239;Radio (12 MB)
Value=240;Radio Cassette Player (4 Batteries)
Value=241;Ticket for Football Game (National League)
Value=242;Videos Club Ticket (Football Match)
Value=243;Weekend Dance Charges (Saturday Night)
Value=244;Text Books(Grade 7)
Value=245;Newspapers
Value=246;Magazines
Value=247;Pen
Value=248;Envelopes
Value=249;School Fees (Private School, Grade 7)
Value=250;Examination Fees (Private School, Grade 9)
Value=251;Rice With Side Dishe (1 plate, Domoda,Yassa, etc.)
Value=252;Tea with Milk (1 Cup of Tea)
Value=253;Hotel (1 Night, Three-Star)
Value=254;Lodge (1 Night, Without Air Conditioner)
Value=255;Beautician (Facial Makeup, 1 Service)
Value=256;Barber (Hair Cut, 1 Service)
Value=257;Beauty Salon (Hair Straightening, 1 Service)
Value=258;Face Powder (Makeup Powder)
Value=259;Nail Polish
Value=260;Razor Blade
Value=261;Shampoo (1000 ml)
Value=262;Skin Light (500 ml)
Value=263;Toilet Soap (200g)
Value=264;Tooth Brush (Adult)
Value=265;Tooth Paste (Colgate 50ml)
Value=266;Necklace (Gold Imitation, Simple)

[Item]
Label=1.8  DISCRIPTION OF ITEM
Name=DISCRIPTION_OF_ITEM
Start=24
Len=3
DataType=Numeric

[ValueSet]
Label=1.8  DISCRIPTION OF ITEM
Name=DISCRIPTION_OF_ITEM_VS1
Value=1;nown brand,white rice(milled) ,sold
Value=2;Paddy rice long grain (Local)
Value=3;Medium-Grained Rice (Imported)
Value=4;brand,white rice(milled) ,sold loose;n
Value=5;Basmati Rice (imported)
Value=6;Whole grain(use 1kg tomato cup)
Value=7;Whole grain(use 1kg tomato cup)
Value=8;Long brown from bakery (Tapalapa)
Value=9;Whole grain(use big tomato cup)
Value=10;white in powdered form
Value=11;ss maximum quantity 250grams, pre
Value=12;of fish (sold loose) 150grams to 200
Value=13;sold loose, 150 grams to 200grams
Value=14;wdered cassava,white in colour(0.5
Value=15;meat and bone, cow/bull
Value=16;meat and bone, sheep
Value=17;whole legs frozen
Value=18;medium size, live local breed
Value=19;meat and bone, pig
Value=20;chicken luncheon,200g
Value=21;meat and bone
Value=22;fresh medium size fish
Value=23;freshly smoked, medium size fish
Value=24;freshly smoked, medium size
Value=25;medium size fish
Value=26;medium size fish
Value=27;medium size fish
Value=28;Small edible crustasean
Value=29;Meduim size Fresh fish
Value=30;Tin Fish 125g,fish in vegaetable oil
Value=31;100-250 grams sold loose
Value=32;fresh egg,imported or local
Value=33;cow milk, unpreserved Fresh Milk
Value=34;Fermented cow milk
Value=35;170 gm, unsweetened (peak milk)
Value=36;125 grams brandless
Value=37;acket 25g (e.g. Vitalait,sophie,cowbe
Value=38;125g,cow milk,fat content 2.5%
Value=39;fresh, without sediments
Value=40;250 gm Salted Butter
Value=41;any brand no sediments
Value=42;Groundnut paste in plastic bundle
Value=43;ic container,(e.g. Rosam, Romi or Re
Value=44;Decorticated nut
Value=45;Sold loose
Value=46;Long Fingers green/yellowish
Value=47;medium size
Value=48;iped fruit,common varieties i.e.(jurr
Value=49;3 to 4 wholes in a heap
Value=50;Golden (green/yellowish)
Value=51;uit that covers the seed; (use 2 kg T
Value=52;A whole paw - paw fruit
Value=53;green one whole fruit
Value=54;Plastic Bag
Value=55;Dried fruit,sold loose
Value=56;clean, intact skin brownish,imported
Value=57;Brown, locally grown vegetable tube
Value=58;ownish vegetable tuber locally grow
Value=59;Common varieties,white beans
Value=60;Red finger shaped, unbroken
Value=61;fresh, medium size vegetable
Value=62;edium size, fresh vegetable,sold loo
Value=63;edium size, fresh vegetable,sold loo
Value=64;m size greenish, fresh vegetable,sold
Value=65;Round onion fresh, big, intact skin
Value=66;fresh, local yellowish in colour
Value=67;big red pepper,Redish in colour
Value=68;fresh, greenish in colour
Value=69;fresh, greenish in colour
Value=70;fresh, green
Value=71;fresh leaves
Value=72;70g,wa-gust'gildaorlitina
Value=73;vegetable ,red roots(Garden product
Value=74;Greenish vegetable fruit
Value=75;sold loose
Value=76;ined, powdered crystallised , sold lo
Value=77;whole, cough candy
Value=78;Hollywood
Value=79;Pure honey, Local Honey
Value=80;In plastic container
Value=81;mintstick (plastic handle)
Value=82;biscuits plain brand (E.g.Bidew)
Value=83;alt, Locally made (Small Cup i.e. 170g
Value=84;Whole
Value=85;jumbo,one cube of 10g
Value=86;ll dried pepper,sold loose in platic b
Value=87;properly boiled
Value=88;whole black seed
Value=89;In plastic bottle container
Value=90;Powdered pepper,sold loose
Value=91;rmanti platic jar,500g plastic/bottle j
Value=92;e-pack 100 bags,sun-island, jolly, olin
Value=93;Instant coffee,Nescafe classic 50 grm
Value=94;owdered tea in tin, commom variet
Value=95;Sweetened local fruit drink
Value=96;een tea in a pack,25g,common varie
Value=97;omestic bottle drink i.e. Fanta 250m
Value=98;300 ml,coctail/malta
Value=99;1.5Ltr in plastic bottle
Value=100;nown brands, i.e. Vodka,whisky and
Value=101;well known brand ,ordinary red win
Value=102;fermented, from palm tree
Value=103;Beer domestic brand,Type:larger
Value=104;inness, (without deposit) Bottle 330
Value=105;20 sticks
Value=106;20 sticks
Value=107;20 sticks
Value=108;Tobacco Powder for smooking
Value=109;20 sticks
Value=110;owdered Tobacco variety ,measure
Value=111;arette papers use for wrapping man
Value=112;Sold loose,medium size
Value=113;Sold loose,medium size
Value=114;polyester 65 % viscose 35%
Value=115;polyester 100%
Value=116;100% Cotton Without brief (4-6 Years)
Value=117;polyester 100% (12-16 Years)
Value=118;ready made trousers (12-16 Years)
Value=119;simple dress polyester 65%
Value=120;simple ladies dress 65% polyester
Value=121;Medium elastrometric fibre, free size
Value=122;100% viscose long sleeved
Value=123;cotton 100%,classic shirt,short sleeve
Value=124;60-65% cotton/ polyester
Value=125;For adult cotton 65%
Value=126;cotton 100% free size
Value=127;cotton 100% polyester long sleeve
Value=128;plain round neck, cotton 100%
Value=129;cotton 100% ,sleevelees (10 Years)
Value=130;80 -90 % synthetic
Value=131;70-100 % cotton,Ankle size socks
Value=132;Tailoring charges for a Pair (Grade 1)
Value=133;Trousers and shirt
Value=134;Rubber sandal shoe for boys (12-16 Years)
Value=135;Plastic shoe for boys (12-16 Years)
Value=136;Plastic for boys (12-16 Years)
Value=137;Immitation with laces,Brandless (12-16 Years)
Value=138; (12-16 Years)
Value=139;Rubber sandal shoe for (12-16 Years)
Value=140;Sole:Synthetic,Classic Shoe (12-16 Years)
Value=141;Sole & heel:Synthetic,Classic Shoe
Value=142;plastic shoe
Value=143;Immitation with laces (e.g. Adidas,Nike)
Value=144;Sole:Synthetic,Classic Shoe
Value=145;plastics shoe
Value=146;House built with cement (Single Room)
Value=147;House built with cement (Room & Parlour)
Value=148;House built with cement
Value=149;House built with cement
Value=150;House built with cement
Value=151;House built with cement
Value=152;Domestics cement
Value=153;Floor ceremic Tile 45 by 45 cm in Size
Value=154;Plumbering T. joint medium
Value=155;3.6 litres
Value=156;18-20 litres
Value=157;20mm ,white,LB
Value=158;3.5 by 3.5 Meter room Size
Value=159;3.5 by 3.5 Meter room Size
Value=160;3.5 by 3.5 Meter room Size
Value=161;1 by 1 meter window
Value=162;10 cubic meter Household consumption
Value=163;Electricity unit consumption charges
Value=164;3 kilogram refiling
Value=165;Use two kilogram tomato cup
Value=166;4-5 sticks,Medium Size
Value=167;Box bed with decorated head
Value=168;Simple metal bed "cham gerejeff"
Value=169;Sofa for sitting room
Value=170;Double bed 8 inches
Value=171;Floor covering (Thick, 1 Meter)
Value=172;Curtains prepacked
Value=173;Round mosquito Net
Value=174;Mat for praying or sleeping (1 by 2 meter)
Value=175;Bed sheet for a Double Bed
Value=176;Height: 120-140cm,medium size,speed:3-5 (Plastic)
Value=177;Chest defrosting,capacity:160L,LWH:60-60-120
Value=178;Tiger generator
Value=179;Locally made iron
Value=180;Single panel with inverters
Value=181;Refrigerator with freezing compartments
Value=182;Kitchen Knife (Stainless steel)
Value=183;Medium (Stainless Steel)
Value=184;Aluminium (7 kg)
Value=185;3.5 liter contaner,plastic
Value=186;20 liter plastic bucket with cover
Value=187;Plastic basin,Thick Plastic Basin
Value=188;Plastic pan,with two handles
Value=189;Batteries (e.g. KK, etc.)
Value=190;Tiger Head
Value=191;Stainless steel container (tubeless)
Value=192;Liquid Bleach
Value=193;Long and white in colour
Value=194;Bop Spray 400ml
Value=195;Rifiling type
Value=196;Safety match box
Value=197;Caustic Soda
Value=198;Locally made soap with caustic soda
Value=199;Natural Starch (i.e."dakandeh")
Value=200;Stick Type:Long stick
Value=201;Energy Saving Light bulb (20 Watts)
Value=202;maid servant full time
Value=203;Service of a driver (Private)
Value=204;250mg
Value=205;75mg
Value=206;Anti-Malaria drug,1 doze
Value=207;400mg
Value=208;1 Strip of 10 Tablets
Value=209;Ticket fee
Value=210;Consultation
Value=211;Consultation
Value=212;Cost of Tooth uproot
Value=213;1 week hospitalization (Public)
Value=214;price for a new car (e.g. Toyota Yaris)
Value=215;Fellenco/Safarri 125cc
Value=216;Mountain bike with Gears
Value=217;Inner tyre (18 by 28 R)
Value=218;one new Inner Tube
Value=219;Repair of puctured tyre of a car
Value=220;Labour charges on break Repair
Value=221;Outer tyre (18 by 28 R)
Value=222;Bosch,capacity 12v 55-60AH
Value=223;used in diesel engines for cars
Value=224;Quantity: 4 to 5 litres, in Plastic bottle
Value=225;Quality:Standard petrol,unleaded
Value=226;seven kilometers
Value=227;Shared Taxi,Fixed Route
Value=228;Fare for one student
Value=229;Return Ticket,six hours
Value=230;Big Ferry,Single Ticket
Value=231;Standard Airmail (e.g. to US, UK, etc.)
Value=232;Standard Letter,destination:National
Value=233;Home base telephone,Brandless
Value=234;Itel,simple phone
Value=235;1hour at cyber café
Value=236;E-Credit ("Nopal")
Value=237;4 GB
Value=238;Brand:LG,LED,32,SAMSUMG
Value=239;12 Meterband Radio
Value=240;4 Batteries/Memory USB
Value=241;GFF First division Leagues
Value=242;Premier league,Spanish League etc
Value=243;Saturday Night Club Ticket
Value=244;Senior Secondary Text Books (Grade 7)
Value=245;Newspaper (e.g. Standard,point,Forroya, etc.)
Value=246;International Magazines (News Week)
Value=247;Blue/Black/Red etc. ink pen
Value=248;Small Size
Value=249;Fees for a Student (Private School, Grade 7)
Value=250;Fees for per Student (Private School, Grade 9)
Value=251;1 plate e.g. Yassa,Domoda, Benachin etc.
Value=252;1 cup of Tea with milk
Value=253;Three Star Hotel 1 Night
Value=254;Lodges Charges per Night without Air Conditioner
Value=255;Facial Makeup
Value=256;Hair cut
Value=257;Hair Straightening (Excluding Cost of Cream)
Value=258;Makeup powder (With Sponge and Mirror)
Value=259;Polish in small glass bottle container
Value=260;stainless Steel
Value=261;750ml,use for bath
Value=262;500ml
Value=263;200g,Santex
Value=264;For Adults,soft brush
Value=265;75ml,colgate
Value=266;Gold Immitation,Simple

[Item]
Label=1.9 Relevance
Name=RELEVANCE
Start=27
Len=1
DataType=Numeric

[ValueSet]
Label=1.9 Relevance
Name=RELEVANCE_VS1
Value=0;Poor
Value=1;Nonpoor

[Item]
Label=1.10. What is the origine of the item
Name=IMP_LOCAL
Start=28
Len=1
DataType=Numeric

[ValueSet]
Label=1.10. What is the origine of the item
Name=IMP_LOCAL_VS1
Value=1;Imported
Value=2;Local

[Item]
Label=1.11. COICOP06( item code)
Name=COICOP06_ITEM_CODE
Start=29
Len=3
DataType=Numeric

[ValueSet]
Label=1.11. COICOP06( item code)
Name=COICOP06_ITEM_CODE_VS1
Value=1;1.1.1.1
Value=2;1.1.1.2
Value=3;1.1.1.3
Value=4;1.1.1.4
Value=5;1.1.1.5
Value=6;1.1.1.6
Value=7;1.1.1.7
Value=8;1.1.1.8
Value=9;1.1.1.9
Value=10;1.1.1.10
Value=11;1.1.1.11
Value=12;1.1.1.12
Value=13;1.1.1.13
Value=14;1.1.1.14
Value=15;1.1.2.1
Value=16;1.1.2.2
Value=17;1.1.2.3
Value=18;1.1.2.4
Value=19;1.1.2.5
Value=20;1.1.2.6
Value=21;1.1.2.7
Value=22;1.1.3.1
Value=23;1.1.3.2
Value=24;1.1.3.3
Value=25;1.1.3.4
Value=26;1.1.3.5
Value=27;1.1.3.6
Value=28;1.1.3.7
Value=29;1.1.3.8
Value=30;1.1.3.9
Value=31;1.1.3.10
Value=32;1.1.4.1
Value=33;1.1.4.2
Value=34;1.1.4.3
Value=35;1.1.4.4
Value=36;1.1.4.5
Value=37;1.1.4.6
Value=38;1.1.4.7
Value=39;1.1.5.1
Value=40;1.1.5.2
Value=41;1.1.5.3
Value=42;1.1.5.4
Value=43;1.1.5.5
Value=44;1.1.6.1
Value=45;1.1.6.2
Value=46;1.1.6.3
Value=47;1.1.6.4
Value=48;1.1.6.5
Value=49;1.1.6.6
Value=50;1.1.6.7
Value=51;1.1.6.8
Value=52;1.1.6.9
Value=53;1.1.6.10
Value=54;1.1.6.11
Value=55;1.1.6.12
Value=56;1.1.7.1
Value=57;1.1.7.2
Value=58;1.1.7.3
Value=59;1.1.7.4
Value=60;1.1.7.5
Value=61;1.1.7.6
Value=62;1.1.7.7
Value=63;1.1.7.8
Value=64;1.1.7.9
Value=65;1.1.7.10
Value=66;1.1.7.11
Value=67;1.1.7.12
Value=68;1.1.7.13
Value=69;1.1.7.14
Value=70;1.1.7.15
Value=71;1.1.7.16
Value=72;1.1.7.17
Value=73;1.1.7.18
Value=74;1.1.7.19
Value=75;1.1.7.20
Value=76;1.1.8.1
Value=77;1.1.8.2
Value=78;1.1.8.3
Value=79;1.1.8.4
Value=80;1.1.8.5
Value=81;1.1.8.6
Value=82;1.1.8.7
Value=83;1.1.9.1
Value=84;1.1.9.2
Value=85;1.1.9.3
Value=86;1.1.9.4
Value=87;1.1.9.5
Value=88;1.1.9.6
Value=89;1.1.9.7
Value=90;1.1.9.8
Value=91;1.1.9.9
Value=92;1.2.1.1
Value=93;1.2.1.2
Value=94;1.2.1.3
Value=95;1.2.2.1
Value=96;1.2.2.2
Value=97;1.2.2.3
Value=98;1.2.2.4
Value=99;1.2.2.5
Value=100;2.1.1.1
Value=101;2.1.2.1
Value=102;2.1.2.2
Value=103;2.1.3.1
Value=104;2.1.3.2
Value=105;2.2.1
Value=106;2.2.2
Value=107;2.2.3
Value=108;2.2.4
Value=109;2.2.5
Value=110;2.2.6
Value=111;2.2.7
Value=112;2.3.1
Value=113;2.3.2
Value=114;3.1.1.1
Value=115;3.1.2.1
Value=116;3.1.2.2
Value=117;3.1.2.3
Value=118;3.1.2.4
Value=119;3.1.2.5
Value=120;3.1.2.6
Value=121;3.1.2.7
Value=122;3.1.2.8
Value=123;3.1.2.9
Value=124;3.1.2.10
Value=125;3.1.2.11
Value=126;3.1.2.12
Value=127;3.1.2.13
Value=128;3.1.2.14
Value=129;3.1.2.15
Value=130;3.1.2.16
Value=131;3.1.2.17
Value=132;3.1.2.18
Value=133;3.1.4.1
Value=134;3.2.1
Value=135;3.2.2
Value=136;3.2.3
Value=137;3.2.4
Value=138;3.2.5
Value=139;3.2.6
Value=140;3.2.7
Value=141;3.2.8
Value=142;3.2.9
Value=143;3.2.10
Value=144;3.2.11
Value=145;3.2.12
Value=146;4.1.1.1
Value=147;4.1.1.2
Value=148;4.1.1.3
Value=149;4.1.1.4
Value=150;4.1.1.5
Value=151;4.1.1.6
Value=152;4.3.1.1
Value=153;4.3.1.2
Value=154;4.3.1.3
Value=155;4.3.1.4
Value=156;4.3.1.5
Value=157;4.3.1.6
Value=158;4.3.1.7
Value=159;4.3.1.8
Value=160;4.3.1.9
Value=161;4.3.1.10
Value=162;4.4.1.1
Value=163;4.5.1
Value=164;4.5.2
Value=165;4.5.4.1
Value=166;4.5.4.2
Value=167;5.1.1.1
Value=168;5.1.1.2
Value=169;5.1.1.3
Value=170;5.1.1.4
Value=171;5.1.2.1
Value=172;5.2.1
Value=173;5.2.2
Value=174;5.2.3
Value=175;5.2.4
Value=176;5.3.1.1
Value=177;5.3.1.2
Value=178;5.3.1.3
Value=179;5.3.1.4
Value=180;5.3.1.5
Value=181;5.3.1.6
Value=182;5.4.1
Value=183;5.4.2
Value=184;5.4.3
Value=185;5.4.4
Value=186;5.4.5
Value=187;5.4.6
Value=188;5.4.7
Value=189;5.5.2.1
Value=190;5.5.2.2
Value=191;5.5.2.3
Value=192;5.6.1.1
Value=193;5.6.1.2
Value=194;5.6.1.3
Value=195;5.6.1.4
Value=196;5.6.1.5
Value=197;5.6.1.6
Value=198;5.6.1.7
Value=199;5.6.1.8
Value=200;5.6.1.9
Value=201;5.6.1.10
Value=202;5.6.2.1
Value=203;5.6.2.2
Value=204;6.1.1.1
Value=205;6.1.1.2
Value=206;6.1.1.3
Value=207;6.1.1.4
Value=208;6.1.1.5
Value=209;6.2.1
Value=210;6.2.1.1
Value=211;6.2.1.2
Value=212;6.2.2.1
Value=213;6.3.1
Value=214;7.1.1.1
Value=215;7.1.2.1
Value=216;7.1.3.1
Value=217;7.2.1.1
Value=218;7.2.1.2
Value=219;7.2.1.3
Value=220;7.2.1.4
Value=221;7.2.1.5
Value=222;7.2.1.6
Value=223;7.2.2.1
Value=224;7.2.2.2
Value=225;7.2.2.3
Value=226;7.3.2.1
Value=227;7.3.2.2
Value=228;7.3.2.3
Value=229;7.3.3.1
Value=230;7.3.4.1
Value=231;8.1.1
Value=232;8.1.2
Value=233;8.2.1
Value=234;8.2.2
Value=235;8.3.1
Value=236;8.3.2
Value=237;9.1.4.1
Value=238;9.2.2.1
Value=239;9.2.2.2
Value=240;9.2.2.3
Value=241;9.4.1.1
Value=242;9.4.2.1
Value=243;9.4.2.2
Value=244;9.5.1.4
Value=245;9.5.2.1
Value=246;9.5.2.2
Value=247;9.5.4.1
Value=248;9.5.4.2
Value=249;10.1
Value=250;10.2
Value=251;11.1.1.1
Value=252;11.1.1.2
Value=253;11.2.1
Value=254;11.2.2
Value=255;12.1.1.1
Value=256;12.1.1.2
Value=257;12.1.1.3
Value=258;12.1.3.1
Value=259;12.1.3.2
Value=260;12.1.3.3
Value=261;12.1.3.4
Value=262;12.1.3.5
Value=263;12.1.3.6
Value=264;12.1.3.7
Value=265;12.1.3.8
Value=266;12.3.1.1

[Item]
Label=1.12. What is the unit of meaurment (UoM)
Name=UOM
Start=32
Len=3
DataType=Numeric

[ValueSet]
Label=1.12. What is the unit of meaurment (UoM)
Name=UOM_VS1
Value=1;4 cups
Value=2;4 cups
Value=3;4 cups
Value=4;4 cups
Value=5;1kg
Value=6;3 cups
Value=7;3 cups
Value=8;2 loaves
Value=9;3 ups
Value=10;3 cups
Value=11;1 packet
Value=12;3 pieces
Value=13;5 pieces
Value=14;500g
Value=15;1kg
Value=16;1 kg
Value=17;1 kg
Value=18;1 whole
Value=19;1 kg
Value=20;1 tin
Value=21;1kg
Value=22;3-4 whole
Value=23;3-4 whole
Value=24;3 whole
Value=25;3 whole
Value=26;3 whole
Value=27;3 cuts
Value=28;3 heaps
Value=29;1 heap
Value=30;1 Tin
Value=31;3 whole
Value=32;1 piece
Value=33;0.5 Liters
Value=34;0.5 Liters
Value=35;1 Tin
Value=36;1 Tin
Value=37;1 sacket
Value=38;1 jar
Value=39;1 liter
Value=40;250g
Value=41;1 liter
Value=42;6 bags
Value=43;1 cup
Value=44;3 cups
Value=45;3 Bags
Value=46;3-4 Pieces
Value=47;4 pieces
Value=48;4 pieces
Value=49;6 heaps
Value=50;4 Pieces
Value=51;1 cup
Value=52;1 whole
Value=53;1 whole
Value=54;3 bags
Value=55;3 bags
Value=56;1 kg
Value=57;3 heaps
Value=58;5 cuts
Value=59;500g
Value=60;5 heaps
Value=61;3 heaps
Value=62;6 pieces
Value=63;6 pieces
Value=64;6 heaps
Value=65;1 kg
Value=66;5 cuts
Value=67;3 heaps
Value=68;5 heaps
Value=69;3 heaps
Value=70;1 whole
Value=71;6 bundles
Value=72;1 tin/sacket
Value=73;3 pieces
Value=74;3 pieces
Value=75;5 bundles
Value=76;4 cups
Value=77;2 pieces
Value=78;1 packet
Value=79;500ml
Value=80;1 liter
Value=81;2 pieces
Value=82;70g-80g
Value=83;3 cups
Value=84;3 Wholes
Value=85;1 cube
Value=86;50 gram
Value=87;50 gram
Value=88;100g
Value=89;1 litre
Value=90;100g
Value=91;1 jar
Value=92;1 packet
Value=93;1 tin
Value=94;1 tin
Value=95;3 bags
Value=96;1 packet
Value=97;1 bottle
Value=98;1 bottle
Value=99;1 bottle
Value=100;1 bottle
Value=101;1 bottle
Value=102;1 liter
Value=103;1 bottle
Value=104;1 bottle
Value=105;1 packet
Value=106;1 packet
Value=107;1 packet
Value=108;50gr
Value=109;1 packet
Value=110;50gr
Value=111;1 packet
Value=112;5 pieces
Value=113;6 pieces
Value=114;1 metre
Value=115;1 piece
Value=116;1 piece
Value=117;1 piece
Value=118;1 piece
Value=119;1 piece
Value=120;1 piece
Value=121;1 piece
Value=122;1 piece
Value=123;1 piece
Value=124;1 piece
Value=125;1 piece
Value=126;1 piece
Value=127;1 piece
Value=128;1 piece
Value=129;1 piece
Value=130;1 piece
Value=131;1 pair
Value=132;1 unit
Value=133;1 service
Value=134;1 pair
Value=135;1 pair
Value=136;1 pair
Value=137;1 pair
Value=138;1 pair
Value=139;1 pair
Value=140;1 pair
Value=141;1 pair
Value=142;1 pair
Value=143;1 pair
Value=144;1 pair
Value=145;1 pair
Value=146;1 month
Value=147;1 month
Value=148;1 month
Value=149;1 month
Value=150;1 month
Value=151;1 month
Value=152;50kg
Value=153;1 square meter
Value=154;1 piece
Value=155;1 tin
Value=156;1 tin
Value=157;1 piece
Value=158;1 service
Value=159;1 service
Value=160;1 service
Value=161;1 piece
Value=162;1 month
Value=163;1 month
Value=164;1 bottle
Value=165;1 cup
Value=166;1 bundle
Value=167;1 unit
Value=168;1 unit
Value=169;1 set
Value=170;1 piece
Value=171;1 meter
Value=172;1 set
Value=173;1 piece
Value=174;1 piece
Value=175;1 piece
Value=176;1 piece
Value=177;1 piece
Value=178;1 piece
Value=179;1 piece
Value=180;1 unit
Value=181;1 piece
Value=182;1 piece
Value=183;1 piece
Value=184;1 piece
Value=185;1 piece
Value=186;1 piece
Value=187;1 piece
Value=188;1 piece
Value=189;1 pair
Value=190;1 piece
Value=191;1 piece
Value=192;1 liter
Value=193;1 piece
Value=194;1 piece
Value=195;1 piece
Value=196;1 box
Value=197;3 bags
Value=198;1 bar
Value=199;3 bags
Value=200;1 packet
Value=201;1 piece
Value=202;1 month
Value=203;1 month
Value=204;30 capsule
Value=205;24 Tablets
Value=206;24 Tablets
Value=207;30 Tablets
Value=208;10 tablets
Value=209;1 service
Value=210;1 service
Value=211;1 service
Value=212;1 service
Value=213;1 Adult
Value=214;1 car
Value=215;1 piece
Value=216; 1 piece
Value=217;1 piece
Value=218;1 piece
Value=219;1 service
Value=220;1 service
Value=221;1 piece
Value=222;1 piece
Value=223;1 litre
Value=224;1 bottle
Value=225;1 liter
Value=226;1 Ticket
Value=227;1 adult
Value=228;1 Ticket
Value=229;1 Ticket
Value=230;1 Ticket
Value=231;1 service
Value=232;1 service
Value=233; 1 piece
Value=234; 1 piece
Value=235;1 Hour
Value=236; 50 units
Value=237;1 piece
Value=238;1 piece
Value=239;1 piece
Value=240;1 piece
Value=241;1 piece
Value=242;1 Ticket
Value=243;1 Ticket
Value=244;1 Ticket
Value=245;1 piece
Value=246;1 piece
Value=247;1  piece
Value=248;1 piece
Value=249;Per Term
Value=250;1 Academic Year
Value=251;1 plate
Value=252;1 cup
Value=253;1 Night
Value=254;1 Night
Value=255;1 service
Value=256;1 service
Value=257;1 service
Value=258;1 piece
Value=259;1 piece
Value=260;1 piece
Value=261;1 piece
Value=262;1 piece
Value=263;1 piece
Value=264;1 piece
Value=265;1 piece
Value=266;1 piece

[Item]
Label=1.13.  What is the quantity
Name=QTY202101
Start=35
Len=7
DataType=Numeric

[Item]
Label=1.14. What is the price
Name=PRICE202101
Start=42
Len=10
DataType=Numeric
Decimal=2
DecimalChar=Yes

[Item]
Label=rCheck
Name=RCHECK
Start=52
Len=1
DataType=Numeric

[ValueSet]
Label=rCheck
Name=RCHECK_VS1
Value=1;Yes
Value=2;No

[Record]
Label=SECTION B: CONTACT PERSON’S DETAILS
Name=SECTION_B_CONTACT_PERSON_S_DETAILS1
RecordTypeValue='5'
RecordLen=526

[Item]
Label=1.5. NAME OF OUTLET
Name=NAME_OF_OUTLET
Start=17
Len=225
DataType=Alpha

[Item]
Label=1.16. TYPE OF OUTLET
Name=TYPE_OF_OUTLET
Start=242
Len=1
DataType=Numeric

[ValueSet]
Label=1.16. TYPE OF OUTLET
Name=TYPE_OF_OUTLET_VS1
Value=1;Open Market
Value=2;Public Service Enterprise
Value=3;Specialised Shop
Value=4;Neighborhood Shop
Value=5;Mechanic
Value=6;Supermaket
Value=7;Ninimarket
Value=8;Departmental Shop
Value=9;vendor

[Item]
Label=1.17. PHONE NUMBER OF OUTLET
Name=PHONE_NUMBER_OF_OUTLET
Start=243
Len=7
DataType=Numeric

[Item]
Label=1.17. 2ND PHONE NUMBER OF OUTLET
Name=ND_PHONE_NUMBER_OF_OUTLET
Start=250
Len=7
DataType=Numeric

[Item]
Label=1.8. EMAIL ADDRESS OF OUTLET
Name=EMAIL_ADDRESS_OF_OUTLET
Start=257
Len=45
DataType=Alpha

[Item]
Label=1.9 STREET ADDRESS OF OUTLET
Name=STREET_ADDRESS_OF_OUTLET
Start=302
Len=45
DataType=Alpha

[Item]
Label=2.1. CONTACT PERSON NAME
Name=CONTACT_PERSON_NAME
Start=347
Len=60
DataType=Alpha

[Item]
Label=2.2. CONTACT PERSON EMAIL
Name=CONTACT_PERSON_EMAIL
Start=407
Len=45
DataType=Alpha

[Item]
Label=2.3. POSITION
Name=POSITION
Start=452
Len=1
DataType=Numeric

[ValueSet]
Label=2.3. POSITION
Name=POSITION_VS1
Value=1;Owner
Value=2;Assistant
Value=3;Other Specify

[Item]
Label=2.3. Other Specify(Position)
Name=OTHER_SPECIFY_POSITION
Start=453
Len=60
DataType=Alpha

[Item]
Label=2.4. Phone Number 1
Name=PHONE_NUMBER_1
Start=513
Len=7
DataType=Numeric

[Item]
Label=2.5. Phone Number 2
Name=PHONE_NUMBER_2
Start=520
Len=7
DataType=Numeric

[Record]
Label=METADATA
Name=METADATA
RecordTypeValue='2'
RecordLen=103

[Item]
Label=Interviewer code
Name=INTERVIEWER_CODE
Start=17
Len=7
DataType=Alpha

[Item]
Label=Interviewer Name
Name=INTERVIEWER_NAME
Start=24
Len=50
DataType=Alpha

[Item]
Label=Interview Start Date
Name=INTERVIEW_START_DATE
Start=74
Len=10
DataType=Alpha

[Item]
Label=Interview Start Time
Name=INTERVIEW_START_TIME
Start=84
Len=10
DataType=Alpha

[Item]
Label=Interview End Time
Name=INTERVIEW_END_TIME
Start=94
Len=10
DataType=Alpha
