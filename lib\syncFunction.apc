﻿function connect_to_server()
//Server credentials
	string host = "http://cpi.gbos.gm/api";
	string user = "admin";
	string pass = "Best12345";
	
	if ! connection() then 
		errmsg("Your tablet is not connected. Please connect to a WIFI or activate the tablet's mobile data.");
		exit 0;
	endif;
	if syncconnect(csweb,  host, user, pass) then 
		exit 1;
	else
		errmsg("We were unable to establish connection with the server.");
		exit 0;
	endif;
end;

function connect_to_backup_server()
//personal server
	string host = "http://cpi.gbos.gm/api";
	string user = "admin";
	string pass = "Best12345";
	
	if ! connection() then 
		errmsg("Your tablet is not connected. Please connect to a WIFI or activate the tablet's mobile data.");
		exit 0;
	endif;
	if syncconnect(csweb,  host, user, pass) then 
		exit 1;
	else
		errmsg("Unable to establish connection with the server.");
		exit 0;
	endif;
end;


function bluetooth_connect(string server_name)
	string server_device = maketext("TAB%s",server_name);
	if syncconnect(bluetooth,server_device) then
		exit 1;
	else
		exit 0;
	endif;
end;
function chechbluetoothName(string server_name)
	if getbluetoothName()<>maketext("TAB%s",server_name) then 
		setbluetoothName(maketext("TAB%s",server_name));
	endif;
end;

//update application
function updateApp()

	//get connection
	if connect_to_server()= 0 then
		reenter;
	endif;
	
	//get the update 
	 syncapp();
	syncdisconnect();
	reenter;

end;




