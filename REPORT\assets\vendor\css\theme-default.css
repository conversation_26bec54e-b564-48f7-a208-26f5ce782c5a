@charset "UTF-8";
.layout-navbar-fixed .layout-wrapper:not(.layout-horizontal) .layout-page:before {
  content: "";
  width: 100%;
  height: 0.75rem;
  position: fixed;
  top: 0px;
  z-index: 10;
}

.bg-menu-theme .menu-header {
  position: relative;
}
.bg-menu-theme .menu-header:before {
  content: "";
  position: absolute;
  left: 0;
  top: 1.1875rem;
  width: 1rem;
  height: 1px;
  transition: all 0.3s ease-in-out;
}
.layout-wrapper:not(.layout-horizontal) .bg-menu-theme .menu-inner .menu-item .menu-link {
  border-radius: 0.375rem;
}
.layout-horizontal .bg-menu-theme .menu-inner > .menu-item > .menu-link {
  border-radius: 0.375rem;
}
@media (min-width: 1200px) {
  .layout-horizontal .bg-menu-theme .menu-inner > .menu-item {
    margin: 0.565rem 0;
  }
  .layout-horizontal .bg-menu-theme .menu-inner > .menu-item:not(:first-child) {
    margin-left: 0.0625rem;
  }
  .layout-horizontal .bg-menu-theme .menu-inner > .menu-item:not(:last-child) {
    margin-right: 0.0625rem;
  }
  .layout-horizontal .bg-menu-theme .menu-inner > .menu-item .menu-sub {
    box-shadow: 0 0.25rem 1rem rgba(161, 172, 184, 0.45);
  }
}
.layout-wrapper:not(.layout-horizontal) .bg-menu-theme .menu-inner > .menu-item.active:before {
  content: "";
  position: absolute;
  right: 0;
  width: 0.25rem;
  height: 2.5rem;
  border-radius: 0.375rem 0 0 0.375rem;
}
.bg-menu-theme .menu-sub > .menu-item > .menu-link:before {
  content: "";
  position: absolute;
  left: 1.4375rem;
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 50%;
}
.layout-horizontal .bg-menu-theme .menu-sub > .menu-item > .menu-link:before {
  left: 1.3rem;
}
.bg-menu-theme .menu-horizontal-wrapper > .menu-inner > .menu-item > .menu-sub > .menu-item > .menu-link:before {
  display: none;
}
.bg-menu-theme .menu-sub > .menu-item.active > .menu-link:not(.menu-toggle):before {
  left: 1.1875rem;
  width: 0.875rem;
  height: 0.875rem;
}
.layout-horizontal .bg-menu-theme .menu-sub > .menu-item.active > .menu-link:not(.menu-toggle):before {
  left: 1.1rem;
}

.layout-menu-hover .layout-menu {
  box-shadow: 0 0.625rem 1.25rem rgba(161, 172, 184, 0.5);
  transition: all 0.3s ease-in-out;
}

.app-brand .layout-menu-toggle {
  position: absolute;
  left: 15rem;
  border-radius: 50%;
}
.app-brand .layout-menu-toggle i {
  width: 1.5rem;
  height: 1.5rem;
  transition: all 0.3s ease-in-out;
}
@media (max-width: 1199.98px) {
  .app-brand .layout-menu-toggle {
    display: none !important;
  }
  .layout-menu-expanded .app-brand .layout-menu-toggle {
    display: block !important;
  }
}

.text-primary {
  color: #696cff !important;
}

.text-body[href]:hover {
  color: #5f61e6 !important;
}

.bg-primary {
  background-color: #696cff !important;
}

a.bg-primary:hover, a.bg-primary:focus {
  background-color: #6467f2 !important;
}

.dropdown-notifications-item:not(.mark-as-read) .dropdown-notifications-read span {
  background-color: #696cff;
}

.bg-label-primary {
  background-color: #e7e7ff !important;
  color: #696cff !important;
}

.border-label-primary {
  border: 3px solid #c3c4ff !important;
}

.border-light-primary {
  border: 3px solid rgba(105, 108, 255, 0.08);
}

.page-item.active .page-link, .page-item.active .page-link:hover, .page-item.active .page-link:focus,
.pagination li.active > a:not(.page-link),
.pagination li.active > a:not(.page-link):hover,
.pagination li.active > a:not(.page-link):focus {
  border-color: #696cff;
  background-color: #696cff;
  color: #fff;
  box-shadow: 0 0.125rem 0.25rem rgba(105, 108, 255, 0.4);
}

.progress-bar {
  background-color: #696cff;
  color: #fff;
  box-shadow: 0 2px 4px 0 rgba(105, 108, 255, 0.4);
}

.list-group-item-primary {
  background-color: #e1e2ff;
  color: #696cff !important;
}

a.list-group-item-primary,
button.list-group-item-primary {
  color: #696cff;
}
a.list-group-item-primary:hover, a.list-group-item-primary:focus,
button.list-group-item-primary:hover,
button.list-group-item-primary:focus {
  background-color: #d6d7f2;
  color: #696cff;
}
a.list-group-item-primary.active,
button.list-group-item-primary.active {
  border-color: #696cff;
  background-color: #696cff;
  color: #696cff;
}

.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus {
  border-color: #696cff;
  background-color: #696cff;
}

.alert-primary {
  background-color: #e7e7ff;
  border-color: #d2d3ff;
  color: #696cff;
}
.alert-primary .btn-close {
  background-image: url("data:image/svg+xml,%3Csvg width='150px' height='151px' viewBox='0 0 150 151' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpolygon id='path-1' points='131.251657 0 74.9933705 56.25 18.7483426 0 0 18.75 56.2450278 75 0 131.25 18.7483426 150 74.9933705 93.75 131.251657 150 150 131.25 93.7549722 75 150 18.75'%3E%3C/polygon%3E%3C/defs%3E%3Cg id='🎨-%5BSetup%5D:-Colors-&amp;-Shadows' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='Artboard' transform='translate(-225.000000, -250.000000)'%3E%3Cg id='Icon-Color' transform='translate(225.000000, 250.500000)'%3E%3Cuse fill='%23696cff' xlink:href='%23path-1'%3E%3C/use%3E%3Cuse fill-opacity='0.5' fill='%23696cff' xlink:href='%23path-1'%3E%3C/use%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.alert-primary .alert-link {
  color: #696cff;
}

.card .alert-primary hr {
  background-color: #696cff !important;
}

.table-primary {
  --bs-table-bg: #e1e2ff;
  --bs-table-striped-bg: #dcdefb;
  --bs-table-striped-color: #435971;
  --bs-table-active-bg: #d1d4f1;
  --bs-table-active-color: #435971;
  --bs-table-hover-bg: #d8daf6;
  --bs-table-hover-color: #435971;
  color: #435971;
  border-color: #d1d4f1;
}
.table-primary th {
  border-bottom-color: inherit !important;
}
.table-primary .btn-icon {
  color: #435971;
}

.btn-primary {
  color: #fff;
  background-color: #696cff;
  border-color: #696cff;
  box-shadow: 0 0.125rem 0.25rem 0 rgba(105, 108, 255, 0.4);
}
.btn-primary:hover {
  color: #fff;
  background-color: #5f61e6;
  border-color: #5f61e6;
  transform: translateY(-1px);
}
.btn-check:focus + .btn-primary, .btn-primary:focus, .btn-primary.focus {
  color: #fff;
  background-color: #5f61e6;
  border-color: #5f61e6;
  transform: translateY(0);
  box-shadow: none;
}
.btn-check:checked + .btn-primary, .btn-check:active + .btn-primary, .btn-primary:active, .btn-primary.active, .show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #595cd9;
  border-color: #595cd9;
}
.btn-check:checked + .btn-primary:focus, .btn-check:active + .btn-primary:focus, .btn-primary:active:focus, .btn-primary.active:focus, .show > .btn-primary.dropdown-toggle:focus {
  box-shadow: none;
}
.btn-primary.disabled, .btn-primary:disabled {
  box-shadow: none;
}

.btn-outline-primary {
  color: #696cff;
  border-color: #696cff;
  background: transparent;
}
.btn-outline-primary:hover {
  color: #fff;
  background-color: #5f61e6;
  border-color: #5f61e6;
  box-shadow: 0 0.125rem 0.25rem 0 rgba(105, 108, 255, 0.4);
  transform: translateY(-1px);
}
.btn-check:focus + .btn-outline-primary, .btn-outline-primary:focus {
  color: #fff;
  background-color: #5f61e6;
  border-color: #5f61e6;
  box-shadow: none;
  transform: translateY(0);
}
.btn-check:checked + .btn-outline-primary, .btn-check:active + .btn-outline-primary, .btn-outline-primary:active, .btn-outline-primary.active, .btn-outline-primary.dropdown-toggle.show {
  color: #fff;
  background-color: #595cd9;
  border-color: #595cd9;
}
.btn-check:checked + .btn-outline-primary:focus, .btn-check:active + .btn-outline-primary:focus, .btn-outline-primary:active:focus, .btn-outline-primary.active:focus, .btn-outline-primary.dropdown-toggle.show:focus {
  box-shadow: none;
}
.btn-outline-primary.disabled, .btn-outline-primary:disabled {
  box-shadow: none;
}

.btn-outline-primary .badge {
  background: #696cff;
  border-color: #696cff;
  color: #fff;
}

.btn-outline-primary:hover .badge,
.btn-outline-primary:focus:hover .badge,
.btn-outline-primary:active .badge,
.btn-outline-primary.active .badge,
.show > .btn-outline-primary.dropdown-toggle .badge {
  background: #fff;
  border-color: #fff;
  color: #696cff;
}

.dropdown-item:not(.disabled).active,
.dropdown-item:not(.disabled):active {
  background-color: rgba(105, 108, 255, 0.08);
  color: #696cff !important;
}

.dropdown-menu > li:not(.disabled) > a:not(.dropdown-item):active,
.dropdown-menu > li.active:not(.disabled) > a:not(.dropdown-item) {
  background-color: rgba(105, 108, 255, 0.08);
  color: #696cff !important;
}

.nav .nav-link:hover, .nav .nav-link:focus {
  color: #5f61e6;
}

.nav-pills .nav-link.active, .nav-pills .nav-link.active:hover, .nav-pills .nav-link.active:focus {
  background-color: #696cff;
  color: #fff;
  box-shadow: 0 2px 4px 0 rgba(105, 108, 255, 0.4);
}

.form-control:focus,
.form-select:focus {
  border-color: #696cff;
}

.input-group:focus-within .form-control,
.input-group:focus-within .input-group-text {
  border-color: #696cff;
}

.form-check-input:focus {
  border-color: #696cff;
  box-shadow: 0 2px 4px 0 rgba(105, 108, 255, 0.4);
}
.form-check-input:disabled {
  background-color: #eceef1;
}
.form-check-input:checked, .form-check-input[type=checkbox]:indeterminate {
  background-color: #696cff;
  border-color: #696cff;
  box-shadow: 0 2px 4px 0 rgba(105, 108, 255, 0.4);
}

.custom-option.checked {
  border: 1px solid #696cff;
}

.form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23696cff'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-control:focus ~ .form-label {
  border-color: #696cff;
}
.form-control:focus ~ .form-label::after {
  border-color: inherit;
}

.divider.divider-primary .divider-text:before, .divider.divider-primary .divider-text:after {
  border-color: #696cff;
}

.navbar.bg-primary {
  background-color: #696cff !important;
  color: #e0e1ff;
}
.navbar.bg-primary .navbar-brand,
.navbar.bg-primary .navbar-brand a {
  color: #fff;
}
.navbar.bg-primary .navbar-brand:hover, .navbar.bg-primary .navbar-brand:focus,
.navbar.bg-primary .navbar-brand a:hover,
.navbar.bg-primary .navbar-brand a:focus {
  color: #fff;
}
.navbar.bg-primary .navbar-search-wrapper .navbar-search-icon,
.navbar.bg-primary .navbar-search-wrapper .search-input {
  color: #e0e1ff;
}
.navbar.bg-primary .search-input-wrapper .search-input,
.navbar.bg-primary .search-input-wrapper .search-toggler {
  background-color: #696cff !important;
  color: #e0e1ff;
}
.navbar.bg-primary .navbar-nav > .nav-link,
.navbar.bg-primary .navbar-nav > .nav-item > .nav-link,
.navbar.bg-primary .navbar-nav > .nav > .nav-item > .nav-link {
  color: #e0e1ff;
}
.navbar.bg-primary .navbar-nav > .nav-link:hover, .navbar.bg-primary .navbar-nav > .nav-link:focus,
.navbar.bg-primary .navbar-nav > .nav-item > .nav-link:hover,
.navbar.bg-primary .navbar-nav > .nav-item > .nav-link:focus,
.navbar.bg-primary .navbar-nav > .nav > .nav-item > .nav-link:hover,
.navbar.bg-primary .navbar-nav > .nav > .nav-item > .nav-link:focus {
  color: #fff;
}
.navbar.bg-primary .navbar-nav > .nav-link.disabled,
.navbar.bg-primary .navbar-nav > .nav-item > .nav-link.disabled,
.navbar.bg-primary .navbar-nav > .nav > .nav-item > .nav-link.disabled {
  color: #b0b2ff !important;
}
.navbar.bg-primary .navbar-nav .show > .nav-link,
.navbar.bg-primary .navbar-nav .active > .nav-link,
.navbar.bg-primary .navbar-nav .nav-link.show,
.navbar.bg-primary .navbar-nav .nav-link.active {
  color: #fff;
}
.navbar.bg-primary .navbar-toggler {
  color: #e0e1ff;
  border-color: rgba(255, 255, 255, 0.15);
}
.navbar.bg-primary .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3Csvg width='14px' height='11px' viewBox='0 0 14 11' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M0,0 L14,0 L14,1.75 L0,1.75 L0,0 Z M0,4.375 L14,4.375 L14,6.125 L0,6.125 L0,4.375 Z M0,8.75 L14,8.75 L14,10.5 L0,10.5 L0,8.75 Z' id='path-1'%3E%3C/path%3E%3C/defs%3E%3Cg id='💎-UI-Elements' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='12)-Navbar' transform='translate(-1174.000000, -1290.000000)'%3E%3Cg id='Group' transform='translate(1174.000000, 1288.000000)'%3E%3Cg id='Icon-Color' transform='translate(0.000000, 2.000000)'%3E%3Cuse fill='rgba(255, 255, 255, 0.8)' xlink:href='%23path-1'%3E%3C/use%3E%3Cuse fill-opacity='0.1' fill='rgba(255, 255, 255, 0.8)' xlink:href='%23path-1'%3E%3C/use%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.navbar.bg-primary .navbar-text {
  color: #e0e1ff;
}
.navbar.bg-primary .navbar-text a {
  color: #fff;
}
.navbar.bg-primary .navbar-text a:hover, .navbar.bg-primary .navbar-text a:focus {
  color: #fff;
}
.navbar.bg-primary hr {
  border-color: rgba(255, 255, 255, 0.15);
}

.menu.bg-primary {
  background-color: #696cff !important;
  color: #e0e1ff;
}
.menu.bg-primary .menu-link,
.menu.bg-primary .menu-horizontal-prev,
.menu.bg-primary .menu-horizontal-next {
  color: #e0e1ff;
}
.menu.bg-primary .menu-link:hover, .menu.bg-primary .menu-link:focus,
.menu.bg-primary .menu-horizontal-prev:hover,
.menu.bg-primary .menu-horizontal-prev:focus,
.menu.bg-primary .menu-horizontal-next:hover,
.menu.bg-primary .menu-horizontal-next:focus {
  color: #fff;
}
.menu.bg-primary .menu-link.active,
.menu.bg-primary .menu-horizontal-prev.active,
.menu.bg-primary .menu-horizontal-next.active {
  color: #fff;
}
.menu.bg-primary .menu-item.disabled .menu-link,
.menu.bg-primary .menu-horizontal-prev.disabled,
.menu.bg-primary .menu-horizontal-next.disabled {
  color: #b0b2ff !important;
}
.menu.bg-primary .menu-item.open:not(.menu-item-closing) > .menu-toggle,
.menu.bg-primary .menu-item.active > .menu-link {
  color: #fff;
}
.menu.bg-primary .menu-item.active > .menu-link:not(.menu-toggle) {
  background-color: #6d70ff;
}
.menu.bg-primary.menu-horizontal .menu-sub > .menu-item.active > .menu-link:not(.menu-toggle) {
  background-color: #7174ff;
}
.menu.bg-primary.menu-horizontal .menu-inner .menu-item:not(.menu-item-closing) > .menu-sub, .menu.bg-primary.menu-horizontal .menu-inner .menu-item.open > .menu-toggle {
  background: #6d70ff;
}
.menu.bg-primary .menu-inner > .menu-item.menu-item-closing .menu-item.open .menu-sub,
.menu.bg-primary .menu-inner > .menu-item.menu-item-closing .menu-item.open .menu-toggle {
  background: transparent;
  color: #e0e1ff;
}
.menu.bg-primary .menu-inner-shadow {
  background: linear-gradient(#696cff 41%, rgba(105, 108, 255, 0.11) 95%, rgba(105, 108, 255, 0));
}
.menu.bg-primary .menu-text {
  color: #fff;
}
.menu.bg-primary .menu-header {
  color: #c2c4ff;
}
.menu.bg-primary hr,
.menu.bg-primary .menu-divider,
.menu.bg-primary .menu-inner > .menu-item.open > .menu-sub::before {
  border-color: rgba(255, 255, 255, 0.15) !important;
}
.menu.bg-primary .menu-inner > .menu-header::before {
  background-color: rgba(255, 255, 255, 0.15);
}
.menu.bg-primary .menu-block::before {
  background-color: #c2c4ff;
}
.menu.bg-primary .menu-inner > .menu-item.open .menu-item.open > .menu-toggle::before {
  background-color: #8385ff;
}
.menu.bg-primary .menu-inner > .menu-item.open .menu-item.active > .menu-link::before {
  background-color: #fff;
}
.menu.bg-primary .menu-inner > .menu-item.open .menu-item.open > .menu-toggle::before,
.menu.bg-primary .menu-inner > .menu-item.open .menu-item.active > .menu-link::before {
  box-shadow: 0 0 0 2px #6d70ff;
}
.menu.bg-primary .ps__thumb-y,
.menu.bg-primary .ps__rail-y.ps--clicking > .ps__thumb-y {
  background: rgba(255, 255, 255, 0.5942917647) !important;
}

.footer.bg-primary {
  background-color: #696cff !important;
  color: #e0e1ff;
}
.footer.bg-primary .footer-link {
  color: #e0e1ff;
}
.footer.bg-primary .footer-link:hover, .footer.bg-primary .footer-link:focus {
  color: #fff;
}
.footer.bg-primary .footer-link.disabled {
  color: #b0b2ff !important;
}
.footer.bg-primary .footer-text {
  color: #fff;
}
.footer.bg-primary .show > .footer-link,
.footer.bg-primary .active > .footer-link,
.footer.bg-primary .footer-link.show,
.footer.bg-primary .footer-link.active {
  color: #fff;
}
.footer.bg-primary hr {
  border-color: rgba(255, 255, 255, 0.15);
}

.bg-primary.toast, .bg-primary.bs-toast {
  color: #fff;
  background-color: rgba(105, 108, 255, 0.85) !important;
  box-shadow: 0 0.25rem 1rem rgba(105, 108, 255, 0.4);
}
.bg-primary.toast .toast-header, .bg-primary.bs-toast .toast-header {
  color: #fff;
}
.bg-primary.toast .toast-header .btn-close, .bg-primary.bs-toast .toast-header .btn-close {
  background-color: #696cff !important;
  background-image: url("data:image/svg+xml,%3Csvg width='150px' height='151px' viewBox='0 0 150 151' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpolygon id='path-1' points='131.251657 0 74.9933705 56.25 18.7483426 0 0 18.75 56.2450278 75 0 131.25 18.7483426 150 74.9933705 93.75 131.251657 150 150 131.25 93.7549722 75 150 18.75'%3E%3C/polygon%3E%3C/defs%3E%3Cg id='🎨-%5BSetup%5D:-Colors-&amp;-Shadows' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='Artboard' transform='translate(-225.000000, -250.000000)'%3E%3Cg id='Icon-Color' transform='translate(225.000000, 250.500000)'%3E%3Cuse fill='%23fff' xlink:href='%23path-1'%3E%3C/use%3E%3Cuse fill-opacity='1' fill='%23fff' xlink:href='%23path-1'%3E%3C/use%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  box-shadow: 0 0.1875rem 0.375rem 0 rgba(105, 108, 255, 0.4) !important;
}

.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {
  color: #696cff;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  color: #696cff;
}
.form-floating > .form-control:-webkit-autofill ~ label {
  color: #696cff;
}

.svg-illustration svg {
  fill: #696cff;
}

html:not([dir=rtl]) .border-primary,
html[dir=rtl] .border-primary {
  border-color: #696cff !important;
}

a {
  color: #696cff;
}
a:hover {
  color: #787bff;
}

.fill-primary {
  fill: #696cff;
}

.bg-navbar-theme {
  background-color: #fff !important;
  color: #697a8d;
}
.bg-navbar-theme .navbar-brand,
.bg-navbar-theme .navbar-brand a {
  color: #566a7f;
}
.bg-navbar-theme .navbar-brand:hover, .bg-navbar-theme .navbar-brand:focus,
.bg-navbar-theme .navbar-brand a:hover,
.bg-navbar-theme .navbar-brand a:focus {
  color: #566a7f;
}
.bg-navbar-theme .navbar-search-wrapper .navbar-search-icon,
.bg-navbar-theme .navbar-search-wrapper .search-input {
  color: #697a8d;
}
.bg-navbar-theme .search-input-wrapper .search-input,
.bg-navbar-theme .search-input-wrapper .search-toggler {
  background-color: #fff !important;
  color: #697a8d;
}
.bg-navbar-theme .navbar-nav > .nav-link,
.bg-navbar-theme .navbar-nav > .nav-item > .nav-link,
.bg-navbar-theme .navbar-nav > .nav > .nav-item > .nav-link {
  color: #697a8d;
}
.bg-navbar-theme .navbar-nav > .nav-link:hover, .bg-navbar-theme .navbar-nav > .nav-link:focus,
.bg-navbar-theme .navbar-nav > .nav-item > .nav-link:hover,
.bg-navbar-theme .navbar-nav > .nav-item > .nav-link:focus,
.bg-navbar-theme .navbar-nav > .nav > .nav-item > .nav-link:hover,
.bg-navbar-theme .navbar-nav > .nav > .nav-item > .nav-link:focus {
  color: #566a7f;
}
.bg-navbar-theme .navbar-nav > .nav-link.disabled,
.bg-navbar-theme .navbar-nav > .nav-item > .nav-link.disabled,
.bg-navbar-theme .navbar-nav > .nav > .nav-item > .nav-link.disabled {
  color: #a5afbb !important;
}
.bg-navbar-theme .navbar-nav .show > .nav-link,
.bg-navbar-theme .navbar-nav .active > .nav-link,
.bg-navbar-theme .navbar-nav .nav-link.show,
.bg-navbar-theme .navbar-nav .nav-link.active {
  color: #566a7f;
}
.bg-navbar-theme .navbar-toggler {
  color: #697a8d;
  border-color: rgba(86, 106, 127, 0.075);
}
.bg-navbar-theme .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3Csvg width='14px' height='11px' viewBox='0 0 14 11' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M0,0 L14,0 L14,1.75 L0,1.75 L0,0 Z M0,4.375 L14,4.375 L14,6.125 L0,6.125 L0,4.375 Z M0,8.75 L14,8.75 L14,10.5 L0,10.5 L0,8.75 Z' id='path-1'%3E%3C/path%3E%3C/defs%3E%3Cg id='💎-UI-Elements' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='12)-Navbar' transform='translate(-1174.000000, -1290.000000)'%3E%3Cg id='Group' transform='translate(1174.000000, 1288.000000)'%3E%3Cg id='Icon-Color' transform='translate(0.000000, 2.000000)'%3E%3Cuse fill='rgba(67, 89, 113, 0.5)' xlink:href='%23path-1'%3E%3C/use%3E%3Cuse fill-opacity='0.1' fill='rgba(67, 89, 113, 0.5)' xlink:href='%23path-1'%3E%3C/use%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.bg-navbar-theme .navbar-text {
  color: #697a8d;
}
.bg-navbar-theme .navbar-text a {
  color: #566a7f;
}
.bg-navbar-theme .navbar-text a:hover, .bg-navbar-theme .navbar-text a:focus {
  color: #566a7f;
}
.bg-navbar-theme hr {
  border-color: rgba(86, 106, 127, 0.075);
}

.layout-navbar {
  background-color: rgba(255, 255, 255, 0.95) !important;
  -webkit-backdrop-filter: saturate(200%) blur(6px);
          backdrop-filter: saturate(200%) blur(6px);
}

.navbar-detached {
  box-shadow: 0 0 0.375rem 0.25rem rgba(161, 172, 184, 0.15);
}

.layout-navbar-fixed .layout-page:before {
  -webkit-backdrop-filter: saturate(200%) blur(10px);
          backdrop-filter: saturate(200%) blur(10px);
  background: rgba(245, 245, 249, 0.6);
}

.bg-menu-theme {
  background-color: #fff !important;
  color: #697a8d;
}
.bg-menu-theme .menu-link,
.bg-menu-theme .menu-horizontal-prev,
.bg-menu-theme .menu-horizontal-next {
  color: #697a8d;
}
.bg-menu-theme .menu-link:hover, .bg-menu-theme .menu-link:focus,
.bg-menu-theme .menu-horizontal-prev:hover,
.bg-menu-theme .menu-horizontal-prev:focus,
.bg-menu-theme .menu-horizontal-next:hover,
.bg-menu-theme .menu-horizontal-next:focus {
  color: #566a7f;
}
.bg-menu-theme .menu-link.active,
.bg-menu-theme .menu-horizontal-prev.active,
.bg-menu-theme .menu-horizontal-next.active {
  color: #566a7f;
}
.bg-menu-theme .menu-item.disabled .menu-link,
.bg-menu-theme .menu-horizontal-prev.disabled,
.bg-menu-theme .menu-horizontal-next.disabled {
  color: #a5afbb !important;
}
.bg-menu-theme .menu-item.open:not(.menu-item-closing) > .menu-toggle,
.bg-menu-theme .menu-item.active > .menu-link {
  color: #566a7f;
}
.bg-menu-theme .menu-item.active > .menu-link:not(.menu-toggle) {
  background-color: #fff;
}
.bg-menu-theme.menu-horizontal .menu-sub > .menu-item.active > .menu-link:not(.menu-toggle) {
  background-color: white;
}
.bg-menu-theme.menu-horizontal .menu-inner .menu-item:not(.menu-item-closing) > .menu-sub, .bg-menu-theme.menu-horizontal .menu-inner .menu-item.open > .menu-toggle {
  background: #fff;
}
.bg-menu-theme .menu-inner > .menu-item.menu-item-closing .menu-item.open .menu-sub,
.bg-menu-theme .menu-inner > .menu-item.menu-item-closing .menu-item.open .menu-toggle {
  background: transparent;
  color: #697a8d;
}
.bg-menu-theme .menu-inner-shadow {
  background: linear-gradient(#fff 41%, rgba(255, 255, 255, 0.11) 95%, rgba(255, 255, 255, 0));
}
.bg-menu-theme .menu-text {
  color: #566a7f;
}
.bg-menu-theme .menu-header {
  color: #8f9baa;
}
.bg-menu-theme hr,
.bg-menu-theme .menu-divider,
.bg-menu-theme .menu-inner > .menu-item.open > .menu-sub::before {
  border-color: transparent !important;
}
.bg-menu-theme .menu-inner > .menu-header::before {
  background-color: transparent;
}
.bg-menu-theme .menu-block::before {
  background-color: #8f9baa;
}
.bg-menu-theme .menu-inner > .menu-item.open .menu-item.open > .menu-toggle::before {
  background-color: white;
}
.bg-menu-theme .menu-inner > .menu-item.open .menu-item.active > .menu-link::before {
  background-color: #566a7f;
}
.bg-menu-theme .menu-inner > .menu-item.open .menu-item.open > .menu-toggle::before,
.bg-menu-theme .menu-inner > .menu-item.open .menu-item.active > .menu-link::before {
  box-shadow: 0 0 0 2px #fff;
}
.bg-menu-theme .ps__thumb-y,
.bg-menu-theme .ps__rail-y.ps--clicking > .ps__thumb-y {
  background: rgba(86, 106, 127, 0.2) !important;
}

.bg-menu-theme .menu-header {
  color: #a1acb8;
}
.bg-menu-theme .menu-header:before {
  background-color: #a1acb8 !important;
}
.bg-menu-theme.menu-vertical {
  box-shadow: 0 0.125rem 0.375rem 0 rgba(161, 172, 184, 0.12);
}
html:not(.layout-menu-collapsed) .bg-menu-theme .menu-inner .menu-item.open > .menu-link, .layout-menu-hover.layout-menu-collapsed .bg-menu-theme .menu-inner .menu-item.open > .menu-link,
html:not(.layout-menu-collapsed) .bg-menu-theme .menu-inner .menu-item .menu-link:not(.active):hover,
.layout-menu-hover.layout-menu-collapsed .bg-menu-theme .menu-inner .menu-item .menu-link:not(.active):hover {
  background-color: rgba(67, 89, 113, 0.04);
}
.bg-menu-theme .menu-inner .menu-sub > .menu-item.active > .menu-link.menu-toggle {
  background-color: rgba(67, 89, 113, 0.04);
}
.bg-menu-theme .menu-inner .menu-sub > .menu-item.active .menu-icon {
  color: #696cff;
}
.bg-menu-theme .menu-inner > .menu-item.active > .menu-link {
  color: #696cff;
  background-color: rgba(105, 108, 255, 0.16) !important;
}
.bg-menu-theme .menu-inner > .menu-item.active:before {
  background: #696cff;
}
.bg-menu-theme .menu-sub > .menu-item > .menu-link:before {
  background-color: #b4bdc6 !important;
}
.bg-menu-theme .menu-sub > .menu-item.active > .menu-link:not(.menu-toggle):before {
  background-color: #696cff !important;
  border: 3px solid #e7e7ff !important;
}

.app-brand .layout-menu-toggle {
  background-color: #696cff;
  border: 7px solid #f5f5f9;
}
.app-brand .layout-menu-toggle i {
  color: #fff;
}
.app-brand .layout-menu-toggle .menu-inner > .menu-header::before {
  background-color: #b4bdc6;
}

.bg-footer-theme {
  background-color: #f5f5f9 !important;
  color: #697a8d;
}
.bg-footer-theme .footer-link {
  color: #697a8d;
}
.bg-footer-theme .footer-link:hover, .bg-footer-theme .footer-link:focus {
  color: #566a7f;
}
.bg-footer-theme .footer-link.disabled {
  color: #a1abb8 !important;
}
.bg-footer-theme .footer-text {
  color: #566a7f;
}
.bg-footer-theme .show > .footer-link,
.bg-footer-theme .active > .footer-link,
.bg-footer-theme .footer-link.show,
.bg-footer-theme .footer-link.active {
  color: #566a7f;
}
.bg-footer-theme hr {
  border-color: rgba(86, 106, 127, 0.0768713725);
}

.layout-footer-fixed .content-footer {
  box-shadow: 0 0 0.375rem 0.25rem rgba(161, 172, 184, 0.15);
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
