"""
Database connection management
"""
import logging
import keyring
from enum import Enum, auto
from typing import Dict, List, Optional
from sqlalchemy import create_engine, inspect
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)

class DatabaseType(Enum):
    """Supported database types"""
    MYSQL = auto()
    POSTGRESQL = auto()
    SQLSERVER = auto()
    SQLITE = auto()
    
    @classmethod
    def get_display_name(cls, db_type) -> str:
        """Get display name for database type"""
        names = {
            cls.MYSQL: "MySQL",
            cls.POSTGRESQL: "PostgreSQL",
            cls.SQLSERVER: "SQL Server",
            cls.SQLITE: "SQLite"
        }
        return names.get(db_type, "Unknown")

class ConnectionStatus(Enum):
    """Connection status enum"""
    DISCONNECTED = auto()
    CONNECTING = auto()
    CONNECTED = auto()
    ERROR = auto()

class ConnectionProfile:
    """Database connection profile"""
    def __init__(
        self,
        name: str,
        db_type: DatabaseType,
        host: str = "",
        port: int = 0,
        database: str = "",
        username: str = "",
        password: str = "",
        connection_string: str = ""
    ):
        self.name = name
        self.db_type = db_type
        self.host = host
        self.port = port
        self.database = database
        self.username = username
        self._password = password
        self.connection_string = connection_string
        
    @property
    def password(self) -> str:
        """Get password from keyring if not set directly"""
        if self._password:
            return self._password
        
        stored_pw = keyring.get_password("csdb-converter", f"{self.name}_{self.username}")
        return stored_pw or ""
    
    @password.setter
    def password(self, value: str):
        """Store password in keyring"""
        self._password = value
        if value:
            keyring.set_password("csdb-converter", f"{self.name}_{self.username}", value)
    
    def get_connection_string(self) -> str:
        """Generate connection string based on database type"""
        if self.connection_string:
            return self.connection_string
            
        if self.db_type == DatabaseType.MYSQL:
            return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
        elif self.db_type == DatabaseType.POSTGRESQL:
            return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
        elif self.db_type == DatabaseType.SQLSERVER:
            return f"mssql+pyodbc://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?driver=ODBC+Driver+17+for+SQL+Server"
        elif self.db_type == DatabaseType.SQLITE:
            return f"sqlite:///{self.database}"
        else:
            raise ValueError(f"Unsupported database type: {self.db_type}")
    
    def to_dict(self) -> Dict:
        """Convert profile to dictionary for serialization"""
        return {
            "name": self.name,
            "db_type": self.db_type.name,
            "host": self.host,
            "port": self.port,
            "database": self.database,
            "username": self.username,
            "connection_string": self.connection_string
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ConnectionProfile':
        """Create profile from dictionary"""
        return cls(
            name=data.get("name", ""),
            db_type=DatabaseType[data.get("db_type", "MYSQL")],
            host=data.get("host", ""),
            port=data.get("port", 0),
            database=data.get("database", ""),
            username=data.get("username", ""),
            connection_string=data.get("connection_string", "")
        )

class ConnectionManager:
    """Manages database connections"""
    def __init__(self):
        self._profiles: List[ConnectionProfile] = []
        self._current_profile: Optional[ConnectionProfile] = None
        self._engine: Optional[Engine] = None
        self._status = ConnectionStatus.DISCONNECTED
        self._error_message = ""
        
    @property
    def profiles(self) -> List[ConnectionProfile]:
        """Get all connection profiles"""
        return self._profiles
    
    @property
    def current_profile(self) -> Optional[ConnectionProfile]:
        """Get current connection profile"""
        return self._current_profile
    
    @property
    def status(self) -> ConnectionStatus:
        """Get connection status"""
        return self._status
    
    @property
    def error_message(self) -> str:
        """Get error message"""
        return self._error_message
    
    @property
    def engine(self) -> Optional[Engine]:
        """Get SQLAlchemy engine"""
        return self._engine
    
    def add_profile(self, profile: ConnectionProfile) -> None:
        """Add a new connection profile"""
        # Check if profile with same name exists
        for i, existing in enumerate(self._profiles):
            if existing.name == profile.name:
                # Replace existing profile
                self._profiles[i] = profile
                logger.info(f"Updated connection profile: {profile.name}")
                return
                
        # Add new profile
        self._profiles.append(profile)
        logger.info(f"Added connection profile: {profile.name}")
    
    def remove_profile(self, name: str) -> bool:
        """Remove a connection profile by name"""
        for i, profile in enumerate(self._profiles):
            if profile.name == name:
                del self._profiles[i]
                logger.info(f"Removed connection profile: {name}")
                
                # Reset current profile if it was removed
                if self._current_profile and self._current_profile.name == name:
                    self._current_profile = None
                    self._status = ConnectionStatus.DISCONNECTED
                    self._engine = None
                
                return True
        
        logger.warning(f"Profile not found: {name}")
        return False
    
    def get_profile(self, name: str) -> Optional[ConnectionProfile]:
        """Get a connection profile by name"""
        for profile in self._profiles:
            if profile.name == name:
                return profile
        return None
    
    def connect(self, profile_name: str) -> bool:
        """Connect to database using the specified profile"""
        profile = self.get_profile(profile_name)
        if not profile:
            self._error_message = f"Profile not found: {profile_name}"
            self._status = ConnectionStatus.ERROR
            logger.error(self._error_message)
            return False
        
        return self.connect_with_profile(profile)
    
    def connect_with_profile(self, profile: ConnectionProfile) -> bool:
        """Connect to database using the provided profile"""
        self._current_profile = profile
        self._status = ConnectionStatus.CONNECTING
        self._error_message = ""
        
        try:
            logger.info(f"Connecting to database: {profile.name}")
            connection_string = profile.get_connection_string()
            self._engine = create_engine(connection_string)
            
            # Test connection
            with self._engine.connect() as conn:
                conn.execute("SELECT 1")
            
            self._status = ConnectionStatus.CONNECTED
            logger.info(f"Connected to database: {profile.name}")
            return True
            
        except SQLAlchemyError as e:
            self._error_message = str(e)
            self._status = ConnectionStatus.ERROR
            self._engine = None
            logger.error(f"Connection error: {e}")
            return False
    
    def disconnect(self) -> None:
        """Disconnect from database"""
        if self._engine:
            self._engine.dispose()
            self._engine = None
        
        self._status = ConnectionStatus.DISCONNECTED
        logger.info("Disconnected from database")
    
    def test_connection(self, profile: ConnectionProfile) -> bool:
        """Test connection to database"""
        try:
            logger.info(f"Testing connection: {profile.name}")
            connection_string = profile.get_connection_string()
            engine = create_engine(connection_string)
            
            with engine.connect() as conn:
                conn.execute("SELECT 1")
            
            engine.dispose()
            logger.info(f"Connection test successful: {profile.name}")
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"Connection test failed: {e}")
            self._error_message = str(e)
            return False
    
    def get_tables(self) -> List[str]:
        """Get list of tables in the current database"""
        if not self._engine:
            logger.warning("Not connected to database")
            return []
        
        try:
            inspector = inspect(self._engine)
            return inspector.get_table_names()
        except SQLAlchemyError as e:
            logger.error(f"Error getting tables: {e}")
            return []
