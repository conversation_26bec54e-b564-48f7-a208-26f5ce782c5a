"""
Main view model
"""
import logging
import threading
from typing import List
from PyQt6.QtCore import QObject, pyqtSignal

from src.ui.viewmodels.connection_viewmodel import ConnectionViewModel
from src.ui.viewmodels.file_viewmodel import FileViewModel
from src.ui.viewmodels.config_viewmodel import ConfigViewModel
from src.ui.viewmodels.progress_viewmodel import ProgressViewModel
from src.config.settings import Settings
from src.core.processors.migration_processor import MigrationProcessor, MigrationStatus

logger = logging.getLogger(__name__)

class MainViewModel(QObject):
    """Main view model"""
    
    # Signals
    status_message = pyqtSignal(str)
    recent_files_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        
        # Create settings
        self.settings = Settings()
        
        # Create child view models
        self.connection_viewmodel = ConnectionViewModel()
        self.file_viewmodel = FileViewModel()
        self.config_viewmodel = ConfigViewModel()
        self.progress_viewmodel = ProgressViewModel()
        
        # Migration processor
        self.migration_processor = None
        self.migration_thread = None
        
        # Connect signals
        self._connect_signals()
    
    def _connect_signals(self):
        """Connect signals between view models"""
        # Connect file view model signals
        self.file_viewmodel.files_changed.connect(self._on_files_changed)
        
        # Connect progress view model signals
        self.progress_viewmodel.status_changed.connect(self._on_status_changed)
    
    def load_settings(self):
        """Load settings"""
        # Load connection profiles
        profile_data = self.settings.load_connection_profiles()
        self.connection_viewmodel.load_profiles(profile_data)
        
        # Set last profile
        last_profile = self.settings.get_last_profile()
        if last_profile:
            self.connection_viewmodel.set_current_profile(last_profile)
        
        # Load configuration
        self.config_viewmodel.set_table_prefix(self.settings.get_table_prefix())
        self.config_viewmodel.set_batch_size(self.settings.get_batch_size())
        self.config_viewmodel.set_encoding(self.settings.get_default_encoding())
        self.config_viewmodel.set_max_concurrent(self.settings.get_max_concurrent_conversions())
        
        # Load recent files
        recent_files = self.settings.get_recent_files()
        self.recent_files_changed.emit()
    
    def save_settings(self):
        """Save settings"""
        # Save connection profiles
        self.settings.save_connection_profiles(self.connection_viewmodel.profiles)
        
        # Save last profile
        if self.connection_viewmodel.current_profile:
            self.settings.set_last_profile(self.connection_viewmodel.current_profile.name)
        
        # Save configuration
        self.settings.set_table_prefix(self.config_viewmodel.table_prefix)
        self.settings.set_batch_size(self.config_viewmodel.batch_size)
        self.settings.set_default_encoding(self.config_viewmodel.encoding)
        self.settings.set_max_concurrent_conversions(self.config_viewmodel.max_concurrent)
    
    def open_file(self, file_path: str):
        """Open a single file"""
        self.file_viewmodel.add_file(file_path)
        self.settings.add_recent_file(file_path)
        self.recent_files_changed.emit()
    
    def open_files(self, file_paths: List[str]):
        """Open multiple files"""
        self.file_viewmodel.add_files(file_paths)
        
        # Add to recent files
        for file_path in file_paths:
            self.settings.add_recent_file(file_path)
        
        self.recent_files_changed.emit()
    
    def get_recent_files(self) -> List[str]:
        """Get list of recent files"""
        return self.settings.get_recent_files()
    
    def clear_recent_files(self):
        """Clear recent files list"""
        self.settings.clear_recent_files()
        self.recent_files_changed.emit()
    
    def start_migration(self):
        """Start or resume migration"""
        if self.progress_viewmodel.status == MigrationStatus.PAUSED:
            # Resume migration
            if self.migration_processor:
                self.migration_processor.resume()
                self.status_message.emit("Migration resumed")
            return
        
        # Check if already running
        if self.migration_thread and self.migration_thread.is_alive():
            self.status_message.emit("Migration already in progress")
            return
        
        # Check if connected to database
        if not self.connection_viewmodel.is_connected:
            self.status_message.emit("Not connected to database")
            return
        
        # Check if files selected
        if not self.file_viewmodel.files:
            self.status_message.emit("No files selected")
            return
        
        # Create migration processor
        self.migration_processor = MigrationProcessor(
            connection_manager=self.connection_viewmodel.connection_manager,
            file_paths=self.file_viewmodel.files,
            table_prefix=self.config_viewmodel.table_prefix,
            batch_size=self.config_viewmodel.batch_size,
            encoding=self.config_viewmodel.encoding
        )
        
        # Set progress callback
        self.migration_processor.set_progress_callback(self._on_migration_progress)
        
        # Reset progress view model
        self.progress_viewmodel.reset()
        
        # Start migration in background thread
        self.migration_thread = threading.Thread(
            target=self._run_migration,
            daemon=True
        )
        self.migration_thread.start()
        
        self.status_message.emit("Migration started")
    
    def pause_migration(self):
        """Pause migration"""
        if self.migration_processor:
            self.migration_processor.pause()
            self.status_message.emit("Migration pause requested")
    
    def cancel_migration(self):
        """Cancel migration"""
        if self.migration_processor:
            self.migration_processor.cancel()
            self.status_message.emit("Migration cancellation requested")
    
    def _run_migration(self):
        """Run migration in background thread"""
        try:
            success = self.migration_processor.migrate()
            
            if success:
                self.status_message.emit("Migration completed successfully")
            else:
                self.status_message.emit(f"Migration failed: {self.migration_processor.progress.error_message}")
                
        except Exception as e:
            logger.exception("Error during migration")
            self.status_message.emit(f"Migration error: {str(e)}")
    
    def _on_migration_progress(self, progress):
        """Handle migration progress updates"""
        # Update progress view model
        self.progress_viewmodel.update_progress(
            percentage=progress.percentage,
            current_file=progress.current_file,
            current_table=progress.current_table,
            status=progress.status,
            error_message=progress.error_message
        )
        
        # Log progress
        if progress.status == MigrationStatus.IN_PROGRESS:
            logger.info(f"Migration progress: {progress.percentage}%")
    
    def _on_files_changed(self):
        """Handle files changed signal"""
        # Reset progress if files change
        self.progress_viewmodel.reset()
    
    def _on_status_changed(self, status):
        """Handle status changed signal"""
        # Log status changes
        logger.info(f"Migration status changed: {status.name}")
