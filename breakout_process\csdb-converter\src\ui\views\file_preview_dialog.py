"""
File preview dialog
"""
import logging
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTreeWidget, QTreeWidgetItem, QTabWidget, QTableWidget,
    QTableWidgetItem, QSplitter, QHeaderView, QTextEdit
)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QFont

from src.core.processors.csdb_processor import CSDBProcessor, CSProField, CSProRecord, CSProDictionary
from src.ui.resources.resource_manager import ResourceManager

logger = logging.getLogger(__name__)

class FilePreviewDialog(QDialog):
    """Dialog for previewing CSDB file structure and data"""
    def __init__(self, parent=None, file_path=None):
        super().__init__(parent)
        
        self.file_path = file_path
        self.processor = None
        
        # Set dialog properties
        self.setWindowTitle("File Preview")
        self.resize(800, 600)
        
        # Initialize UI
        self._init_ui()
        
        # Load file if provided
        if self.file_path:
            self.load_file(self.file_path)
    
    def _init_ui(self):
        """Initialize UI components"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        
        # Create file info section
        info_layout = QHBoxLayout()
        main_layout.addLayout(info_layout)
        
        # File label
        self.file_label = QLabel("File: ")
        self.file_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        info_layout.addWidget(self.file_label)
        
        info_layout.addStretch()
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Create structure tab
        self.structure_widget = QWidget()
        self.tab_widget.addTab(self.structure_widget, "Structure")
        
        # Create structure layout
        structure_layout = QVBoxLayout(self.structure_widget)
        
        # Create tree widget for dictionary structure
        self.tree_widget = QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Type", "Length", "Occurrences", "Start Pos", "Label"])
        self.tree_widget.setAlternatingRowColors(True)
        structure_layout.addWidget(self.tree_widget)
        
        # Create data tab
        self.data_widget = QWidget()
        self.tab_widget.addTab(self.data_widget, "Data Preview")
        
        # Create data layout
        data_layout = QVBoxLayout(self.data_widget)
        
        # Create record type selection
        record_layout = QHBoxLayout()
        data_layout.addLayout(record_layout)
        
        record_layout.addWidget(QLabel("Record Type:"))
        
        self.record_combo = QComboBox()
        self.record_combo.currentIndexChanged.connect(self._on_record_type_changed)
        record_layout.addWidget(self.record_combo)
        
        record_layout.addStretch()
        
        # Create table widget for data preview
        self.table_widget = QTableWidget()
        self.table_widget.setAlternatingRowColors(True)
        data_layout.addWidget(self.table_widget)
        
        # Create button layout
        button_layout = QHBoxLayout()
        main_layout.addLayout(button_layout)
        
        button_layout.addStretch()
        
        # Create close button
        self.close_button = QPushButton("Close")
        self.close_button.setIcon(ResourceManager.get_icon("exit.png"))
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)
    
    def load_file(self, file_path):
        """Load CSDB file"""
        self.file_path = file_path
        self.file_label.setText(f"File: {file_path}")
        
        try:
            # Create processor
            self.processor = CSDBProcessor(file_path)
            
            # Validate file
            if not self.processor.validate():
                logger.error(f"Invalid CSDB file: {file_path}")
                return
            
            # Read header
            if not self.processor.read_header():
                logger.error(f"Failed to read CSDB header: {file_path}")
                return
            
            # Display dictionary structure
            self._display_structure()
            
            # Load record types for data preview
            self._load_record_types()
            
            # Read some data for preview
            self._read_preview_data()
            
        except Exception as e:
            logger.error(f"Error loading file: {e}")
    
    def _display_structure(self):
        """Display dictionary structure in tree widget"""
        if not self.processor or not self.processor.dictionary:
            return
        
        # Clear tree
        self.tree_widget.clear()
        
        # Add dictionary root
        dict_item = QTreeWidgetItem(self.tree_widget)
        dict_item.setText(0, self.processor.dictionary.name)
        dict_item.setText(5, self.processor.dictionary.label)
        dict_item.setIcon(0, ResourceManager.get_icon("database.png"))
        
        # Add records
        for record in self.processor.dictionary.records:
            record_item = QTreeWidgetItem(dict_item)
            record_item.setText(0, record.record_type)
            record_item.setText(5, record.label)
            record_item.setIcon(0, ResourceManager.get_icon("file.png"))
            
            # Add fields
            for field in record.fields:
                field_item = QTreeWidgetItem(record_item)
                field_item.setText(0, field.name)
                field_item.setText(1, field.data_type.name)
                field_item.setText(2, str(field.length))
                field_item.setText(3, str(field.occurrences))
                field_item.setText(4, str(field.start_pos))
                field_item.setText(5, field.label)
                field_item.setIcon(0, ResourceManager.get_icon("radio.png"))
        
        # Expand dictionary
        self.tree_widget.expandItem(dict_item)
        
        # Resize columns
        for i in range(self.tree_widget.columnCount()):
            self.tree_widget.resizeColumnToContents(i)
    
    def _load_record_types(self):
        """Load record types into combo box"""
        if not self.processor or not self.processor.dictionary:
            return
        
        # Clear combo
        self.record_combo.clear()
        
        # Add record types
        for record in self.processor.dictionary.records:
            self.record_combo.addItem(record.record_type)
    
    def _on_record_type_changed(self, index):
        """Handle record type selection change"""
        if index < 0 or not self.processor or not self.processor.dictionary:
            return
        
        # Get selected record type
        record_type = self.record_combo.currentText()
        
        # Display data for selected record type
        self._display_data(record_type)
    
    def _read_preview_data(self):
        """Read some data for preview"""
        if not self.processor or not self.processor.dictionary:
            return
        
        try:
            # Read first 100 records for preview
            with open(self.processor.file_path, "rb") as f:
                # Skip header
                f.seek(4)  # Skip signature
                f.read(4)  # Skip version
                
                # Skip dictionary
                self.processor._skip_dictionary(f)
                
                # Read data section signature
                data_sig = f.read(4)
                if data_sig != b'DATA':
                    logger.error("Invalid data section signature")
                    return
                
                # Read record count
                import struct
                total_records = struct.unpack("<I", f.read(4))[0]
                
                # Initialize data dictionary
                self.processor.data = {record.record_type: [] for record in self.processor.dictionary.records}
                
                # Read up to 100 records for preview
                preview_count = min(100, total_records)
                
                for _ in range(preview_count):
                    # Read record type
                    record_type_len = struct.unpack("<I", f.read(4))[0]
                    record_type = f.read(record_type_len).decode(self.processor.encoding)
                    
                    # Get record definition
                    record_def = self.processor.dictionary.get_record(record_type)
                    if not record_def:
                        logger.warning(f"Unknown record type: {record_type}")
                        continue
                    
                    # Read record data
                    record_data = {}
                    for field in record_def.fields:
                        field_data = self.processor._read_field_data(f, field)
                        record_data[field.name] = field_data
                    
                    # Add to data dictionary
                    self.processor.data[record_type].append(record_data)
            
            # Display data for first record type
            if self.record_combo.count() > 0:
                self._display_data(self.record_combo.currentText())
                
        except Exception as e:
            logger.error(f"Error reading preview data: {e}")
    
    def _display_data(self, record_type):
        """Display data for selected record type"""
        if not self.processor or not self.processor.dictionary:
            return
        
        # Get record definition
        record_def = self.processor.dictionary.get_record(record_type)
        if not record_def:
            return
        
        # Get data for record type
        data = self.processor.data.get(record_type, [])
        
        # Clear table
        self.table_widget.clear()
        
        # Set column count
        self.table_widget.setColumnCount(len(record_def.fields))
        
        # Set row count
        self.table_widget.setRowCount(len(data))
        
        # Set headers
        headers = []
        for field in record_def.fields:
            header = f"{field.name}\n{field.data_type.name} ({field.length})"
            if field.occurrences > 1:
                header += f" [{field.occurrences}]"
            headers.append(header)
        
        self.table_widget.setHorizontalHeaderLabels(headers)
        
        # Set data
        for row, record_data in enumerate(data):
            for col, field in enumerate(record_def.fields):
                value = record_data.get(field.name, "")
                
                # Format value for display
                if isinstance(value, list):
                    # Array field
                    display_value = ", ".join(str(v) for v in value)
                else:
                    display_value = str(value)
                
                item = QTableWidgetItem(display_value)
                self.table_widget.setItem(row, col, item)
        
        # Resize columns
        self.table_widget.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

# Import at the end to avoid circular imports
from PyQt6.QtWidgets import QComboBox
