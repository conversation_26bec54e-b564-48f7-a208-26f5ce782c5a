"""
Main application window
"""
import os
import logging
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QLabel, QPushButton, QFileDialog, QMessageBox, QTabWidget,
    QStatusBar, QMenuBar, QMenu, QToolBar
)
from PyQt6.QtGui import QAction
from PyQt6.QtCore import Qt, QSize

from src.ui.views.connection_panel import ConnectionPanel
from src.ui.views.file_selection_panel import FileSelectionPanel
from src.ui.views.configuration_panel import ConfigurationPanel
from src.ui.views.progress_panel import ProgressPanel
from src.ui.viewmodels.main_viewmodel import MainViewModel
from src.ui.resources.resource_manager import ResourceManager

logger = logging.getLogger(__name__)

class MainWindow(QMainWindow):
    """Main application window"""
    def __init__(self):
        super().__init__()

        # Set window properties
        self.setWindowTitle("CSPro Database Converter")
        self.setMinimumSize(QSize(900, 700))

        # Create view model
        self.view_model = MainViewModel()

        # Initialize UI
        self._init_ui()

        # Connect signals
        self._connect_signals()

        # Load settings
        self.view_model.load_settings()

    def _init_ui(self):
        """Initialize UI components"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Create splitter for main content
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(self.main_splitter)

        # Create connection panel
        self.connection_panel = ConnectionPanel(self.view_model.connection_viewmodel)
        self.main_splitter.addWidget(self.connection_panel)

        # Create right side container
        right_container = QWidget()
        right_layout = QVBoxLayout(right_container)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(10)
        self.main_splitter.addWidget(right_container)

        # Create file selection panel
        self.file_selection_panel = FileSelectionPanel(self.view_model.file_viewmodel)
        right_layout.addWidget(self.file_selection_panel)

        # Create configuration panel
        self.config_panel = ConfigurationPanel(self.view_model.config_viewmodel)
        right_layout.addWidget(self.config_panel)

        # Create progress panel
        self.progress_panel = ProgressPanel(self.view_model.progress_viewmodel)
        right_layout.addWidget(self.progress_panel)

        # Set splitter sizes
        self.main_splitter.setSizes([250, 650])

        # Create status bar
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("Ready")

        # Create menu bar
        self._create_menu_bar()

        # Create toolbar
        self._create_toolbar()

    def _create_menu_bar(self):
        """Create application menu bar"""
        menu_bar = self.menuBar()

        # File menu
        file_menu = menu_bar.addMenu("&File")

        # Open action
        open_action = QAction("&Open CSDB File...", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self._on_open_file)
        file_menu.addAction(open_action)

        # Recent files submenu
        self.recent_menu = QMenu("Recent Files", self)
        file_menu.addMenu(self.recent_menu)

        file_menu.addSeparator()

        # Exit action
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut("Alt+F4")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Tools menu
        tools_menu = menu_bar.addMenu("&Tools")

        # Settings action
        settings_action = QAction("&Settings...", self)
        settings_action.triggered.connect(self._on_settings)
        tools_menu.addAction(settings_action)

        # Help menu
        help_menu = menu_bar.addMenu("&Help")

        # About action
        about_action = QAction("&About", self)
        about_action.triggered.connect(self._on_about)
        help_menu.addAction(about_action)

    def _connect_signals(self):
        """Connect signals to slots"""
        # Connect view model signals
        self.view_model.status_message.connect(self.statusBar.showMessage)
        self.view_model.recent_files_changed.connect(self._update_recent_files_menu)

        # Connect panel signals
        self.progress_panel.start_clicked.connect(self.view_model.start_migration)
        self.progress_panel.pause_clicked.connect(self.view_model.pause_migration)
        self.progress_panel.cancel_clicked.connect(self.view_model.cancel_migration)

        # Update recent files menu
        self._update_recent_files_menu()

    def _update_recent_files_menu(self):
        """Update recent files menu"""
        self.recent_menu.clear()

        recent_files = self.view_model.get_recent_files()
        if not recent_files:
            no_recent = QAction("No Recent Files", self)
            no_recent.setEnabled(False)
            self.recent_menu.addAction(no_recent)
            return

        for file_path in recent_files:
            action = QAction(os.path.basename(file_path), self)
            action.setToolTip(file_path)
            action.triggered.connect(lambda checked, path=file_path: self.view_model.open_file(path))
            self.recent_menu.addAction(action)

        self.recent_menu.addSeparator()
        clear_action = QAction("Clear Recent Files", self)
        clear_action.triggered.connect(self.view_model.clear_recent_files)
        self.recent_menu.addAction(clear_action)

    def _create_toolbar(self):
        """Create application toolbar"""
        # Main toolbar
        self.main_toolbar = QToolBar("Main Toolbar")
        self.main_toolbar.setIconSize(QSize(24, 24))
        self.main_toolbar.setMovable(False)
        self.addToolBar(self.main_toolbar)

        # Add file open action
        self.open_action = QAction("Open File", self)
        self.open_action.setIcon(ResourceManager.get_icon("file.png"))
        self.open_action.triggered.connect(self._on_open_file)
        self.main_toolbar.addAction(self.open_action)
        self.main_toolbar.addSeparator()

        # Add start conversion action
        self.start_action = QAction("Start Conversion", self)
        self.start_action.setIcon(ResourceManager.get_icon("start.png"))
        self.start_action.triggered.connect(self.view_model.start_migration)
        self.main_toolbar.addAction(self.start_action)

        # Add pause conversion action
        self.pause_action = QAction("Pause Conversion", self)
        self.pause_action.setIcon(ResourceManager.get_icon("pause.png"))
        self.pause_action.triggered.connect(self.view_model.pause_migration)
        self.main_toolbar.addAction(self.pause_action)

        # Add cancel conversion action
        self.cancel_action = QAction("Cancel Conversion", self)
        self.cancel_action.setIcon(ResourceManager.get_icon("stop.png"))
        self.cancel_action.triggered.connect(self.view_model.cancel_migration)
        self.main_toolbar.addAction(self.cancel_action)

        self.main_toolbar.addSeparator()

        # Add settings action
        self.settings_action = QAction("Settings", self)
        self.settings_action.setIcon(ResourceManager.get_icon("settings.png"))
        self.settings_action.triggered.connect(self._on_settings)
        self.main_toolbar.addAction(self.settings_action)

        self.main_toolbar.addSeparator()

        # Add help action
        self.help_action = QAction("Help", self)
        self.help_action.setIcon(ResourceManager.get_icon("help.png"))
        self.help_action.triggered.connect(self._on_about)
        self.main_toolbar.addAction(self.help_action)

    def _on_open_file(self):
        """Handle open file action"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "Open CSDB Files",
            "",
            "CSPro Database Files (*.csdb);;All Files (*.*)"
        )

        if file_paths:
            self.view_model.open_files(file_paths)

    def _on_settings(self):
        """Handle settings action"""
        # TODO: Show settings dialog
        QMessageBox.information(self, "Settings", "Settings dialog not implemented yet")

    def _on_about(self):
        """Handle about action"""
        QMessageBox.about(
            self,
            "About CSPro Database Converter",
            "CSPro Database Converter\n\n"
            "Version 1.0.0\n\n"
            "A tool for converting CSPro survey databases to SQL databases."
        )

    def closeEvent(self, event):
        """Handle window close event"""
        # Save settings
        self.view_model.save_settings()

        # Accept close event
        event.accept()
