﻿{Application 'CPI_CODEBOOK' logic file generated by CSPro}
PROC GLOBAL
//FUNCTION TO OVERWRITE THE BACK BUTTON WHEN EXITING THE APPLICATION

valueset available_items_vs;

// Array to track which items have been selected
array selected_items(100); // Adjust size based on your maximum code value
valueset temp_vs;
list numeric selectedItems;  // A dynamic list to store selected items
numeric max_allowed = 5; //max allowed items to be selected
string capi_items_name_price;

function OnStop()

	numeric response = errmsg("Do you want to save the data before quitting?")
		select("Yes", continue, "No", continue, "Cancel", continue);

	if response = 3 then
		// Cancel
		reenter;

	elseif response = 1 then
		// Save and quit
		savepartial();
	endif;
	stop(1); // close the program

end;


// ------------------
// ------------------ GPS
// ------------------
numeric max_time = 60;
numeric desired_accuracy = 20;
string capi_gps_reading = "Take GPS reading?";
string capi_gps_data_status;
string capi_gps_accuracy_status_label;
string capi_gps_accuracy_status;

// // Verify whether all GPS data values exist
// function IsCompleteGPSData()
	// IsCompleteGPSData = 0;
	// if H12_GPS_LATITUDE <> notappl
		// and H12_GPS_LONGITUDE <> notappl
		// and GPS_ALTITUDE <> notappl
		// and GPS_ACCURACY <> notappl
		// and GPS_READTIME <> notappl then
		// IsCompleteGPSData = 1;
		
	// endif;
// end;

// // Verify whether GPS accuracy is within defined range
// function IsDesiredAccuracy()

	// IsDesiredAccuracy = 0;
	// if 0 <= GPS_ACCURACY and GPS_ACCURACY <= desired_accuracy then
		// IsDesiredAccuracy = 1;
	// endif;

// end;

// Assign result of GPS reading
// function AssignGPSValues()

	// AssignGPSValues = 0;
	// numeric input = 1;
	// if IsCompleteGPSData() then

		// if gps(accuracy) > GPS_ACCURACY then
			// // Prompt user before replacing GPS data with less accurate GPS data
			// input = warning("Previous GPS data (accuracy = %d) will be replaced with less "
				// "accurate GPS data (accuracy = %d).", GPS_ACCURACY, gps(accuracy))
				// select("Confirm", continue, "Cancel", continue);

		// else
			// // Prompt user before replacing GPS data
			// input = warning("Previous GPS data will be replaced.")
				// select("Confirm", continue, "Cancel", continue);

		// endif

	// endif;

	// if input <> 2 then
		// H12_GPS_LATITUDE = gps(latitude);
		// H12_GPS_LONGITUDE = gps(longitude);
		// GPS_ALTITUDE = gps(altitude);
		// GPS_ACCURACY = gps(accuracy);
		// GPS_READTIME = gps(readtime);
		// AssignGPSValues = 1;
		
	// endif;

// end;

// // Delete GPS data
// function DeleteGPSData()

	// H12_GPS_LATITUDE = notappl;
	// H12_GPS_LONGITUDE = notappl;
	// GPS_ALTITUDE = notappl;
	// GPS_ACCURACY = notappl;
	// GPS_READTIME = notappl;

// end;

// // Take GPS reading
// function TakeGPSReading()

	// if gps(open) then

		// numeric result = gps(read, max_time, desired_accuracy);
		// if result = 1 then

			// // Read was successful
			// if AssignGPSValues() then
				// capi_gps_reading = "Take GPS reading?";

			// else
				// capi_gps_reading = "You chose not to update GPS data. Retake GPS reading?";
				// reenter;
			// endif;
			
			// //READING CANCELED BY USER
		// elseif result = -1 then
			// capi_gps_reading = "GPS reading cancelled by user. Retake GPS reading?";
			// reenter;
			// //READING WAS FAILED 
		// elseif result = 0 then
			// capi_gps_reading = "GPS reading failed. Retake GPS reading?";
			// reenter;

		// endif;

		// gps(close);

	// else
		// //GPS START FAILED
		// capi_gps_reading = "Failed to start GPS. Retake GPS reading?";
		// reenter;

	// endif;
	

// end;


// map mymap; 
  // function showMap(numeric getLat, numeric getLong)
	// mymap.setBaseMap(Hybrid); 

	// numeric markerID = mymap.addmarker(getLat, getlong);
	
	// mymap.setmarkertext(markerID, maketext("%v", CMP_NUM), "white", "black");
	// //check and define string literals based on the results code
	// string intvR,results;
	// // if HH11_RESULTS_OF_INTERVIEW = 1 then 
		// // intvR = "Completed";
		// // results = maketext("<b> Results : </b> %v <br/>", intvR);
	// // endif;
	
	// string ea_displayText = maketext("<b> EA Code : </b> %v <br/>", HH1_EA);
	
	
	// string popupText = maketext("%s <b> HH Number : </b> %v <br/>   <b> Head: </b> %s %s<br/> <b> Total Members: </b> %d <br/> %s",
		// ea_displayText, HH2,
        // strip(HL2_FIRST_NAME), 
        // strip(HL2_LAST_NAME), 
        // TOT_MEM, results); 
	// mymap.setMarkerDescription(markerId, popupText ); 
	
	// mymap.show();
	// myMap.addTextButton("Close", closeMap(myMap)); 
  
  // end;

PROC CPI_CODEBOOK_FF

PROC CPI_CODEBOOK_LEVEL
preproc
INTERVIEWER_CODE = getoperatorid();
if loadcase(STAFF_DICT, INTERVIEWER_CODE) then 
	INTERVIEWER_NAME = S_NAME;
endif;

if demode() = 1 then 
	INTERVIEW_START_DATE = edit("99/99/9999", sysdate("ddmmyyyy"));
	INTERVIEW_START_TIME = edit("99:99:99", systime("HHMMSS"));
endif;
PROC OUTLET_UUID
preproc

if demode() = 1 then 
 $ = uuid();
endif;
PROC R_TYPE
 
PROC ENUMERATION_MARKET_EM

postproc

SETTLEMENT = $;


// if $ in 1:2 or 23 then 
	// LGA = 4;
	// elseif $ in 3:4 or 24 then 
	// LGA = 7;
	// elseif $ in 5:7 or 27 then
	// LGA = 8; 
	// elseif $ in 8:9 or 26 then 
	// LGA = 6; 
	// elseif $ in 10:13 or 28 then 
	// LGA = 5;
	// elseif $ = 14 then
	// LGA = 1;
	// elseif $ in 18:22 then
	// LGA = 3;
	// elseif $ in 15 : 17 then 
	// LGA =2; 

// endif;

numeric manual_choice = errmsg("You are now working in %s Market Enumeration?" ,getlabel(ENUMERATION_MARKET_EM by code, $))
						select("Continue", continue, "Change", reenter);
DISTRICT_CODE = $;	

// if manual_choice =2 then
	// reenter;
	
// endif;
PROC LGA



// if $ in 1:2 or 23 then 
	// LGA = 4;
	// elseif $ in 3:4 or 24 then 
	// LGA = 7;
	// elseif $ in 5:7 or 27 then
	// LGA = 8; 
	// elseif $ in 8:9 or 26 then 
	// LGA = 6; 
	// elseif $ in 10:13 or 28 then 
	// LGA = 5;
	// elseif $ = 14 then
	// LGA = 1;
	// elseif $ in 18:22 then
	// LGA = 3;
	// elseif $ in 15 : 17 then 
	// LGA =2; 

// endif;
preproc
if ENUMERATION_MARKET_EM = 3 then         
	LGA = 7;
	elseif ENUMERATION_MARKET_EM = 4 then
	LGA = 7;
	elseif ENUMERATION_MARKET_EM = 24 then
	LGA = 7;
	elseif  ENUMERATION_MARKET_EM = 1 then 
	LGA = 4;
	elseif  ENUMERATION_MARKET_EM = 2 then
	LGA = 4;
	elseif  ENUMERATION_MARKET_EM = 23 then
	LGA = 4;
	elseif ENUMERATION_MARKET_EM = 5  then
	LGA = 8; 
	elseif ENUMERATION_MARKET_EM = 6  then
	LGA = 8; 
	elseif ENUMERATION_MARKET_EM = 7 then
	LGA = 8;
	elseif ENUMERATION_MARKET_EM = 27 then
	LGA = 8;
	elseif ENUMERATION_MARKET_EM = 8 then 
	LGA = 6; 
	elseif ENUMERATION_MARKET_EM = 9 then
	LGA = 6;
	elseif ENUMERATION_MARKET_EM = 26 then
	LGA = 6;
	elseif ENUMERATION_MARKET_EM = 10 then 
	LGA = 5;
	elseif ENUMERATION_MARKET_EM = 11 then 
	LGA = 5;
	elseif ENUMERATION_MARKET_EM = 12 then 
	LGA = 5;
	elseif ENUMERATION_MARKET_EM = 13 then 
	LGA = 5;
	elseif ENUMERATION_MARKET_EM = 28 then 
	LGA = 5;
	elseif ENUMERATION_MARKET_EM = 14 then
	LGA = 1;
	elseif ENUMERATION_MARKET_EM = 18 then
	LGA = 3;
	elseif ENUMERATION_MARKET_EM = 19 then
	LGA = 3;
	elseif ENUMERATION_MARKET_EM = 20 then
	LGA = 3;
	elseif ENUMERATION_MARKET_EM = 21 then
	LGA = 3;
	elseif ENUMERATION_MARKET_EM = 22 then
	LGA = 3;
	elseif ENUMERATION_MARKET_EM in 15 then 
	LGA =2;
	elseif ENUMERATION_MARKET_EM = 16 then 
	LGA =2;	
	elseif ENUMERATION_MARKET_EM = 17 then 
	LGA =2;	

endif;
PROC DISTRICT_CODE
postproc
 DISTRICT__NAME= getlabel(DISTRICT_CODE by code, $);
PROC DISTRICT__NAME

//$ = getlabel(DISTRICT_CODE by code, DISTRICT_CODE));
PROC INTRODUCTION



PROC INTERVIEW_RESULT

preproc

if INTRODUCTION = 1 then 
	$ = 1;
	protect($,true);
	else
	setvalueset($, INTERVIEW_RESULT_VS2);
	protect($, false);
endif;

postproc
if INTERVIEW_RESULT = 3 then 
	errmsg("Be sure to consult your supervisor before confirming")
	select("Continue", continue, "No", $) default(2);
	endlevel;
	elseif $ = 2 then 
	errmsg("Saving partially, Revisit expected")
	select("Continue", continue, "No",$) default(2);
	savepartial();
	endlevel;
	exit;
endif;
PROC NAME_OF_OUTLET

// onfocus
// if OUTLET_ID <> 1 then 
	// numeric check= errmsg("proceed to the next outlet")
	// select("Yes", continue, "No", OUTLET_ID(curocc() -1),"Unavailable", continue);
	
	// if check = 3 then
		// skip to next;
	// endif;
// endif;

postproc 
CheckName($);


// //set case label for easy identification
setcaselabel(CPI_CODEBOOK_DICT, maketext("EM: %s  | Outlet: %s ", 
				 getvaluelabel(ENUMERATION_MARKET_EM),
				 getvaluelabel($)
				// getvaluelabel(COICOP06_ITEM_CODE)
				 ));
PROC SECTION_A_IDENTIFICATION_OF_OUTLET1000


// if curocc() > 1 then
        // valueset items_vs = NAME_OF_ITEM_VS1;  // Copy the original value set
        // numeric i;
        // for i = 1 to curocc() - 1 do
            // if NAME_OF_ITEM(i) <> 0 then  // Check if a valid selection exists in previous occurrence
                // available_items_vs.remove(NAME_OF_ITEM(i));  // Remove the selected item from the valueset
            // endif;
        // endfor;
        // setvalueset(NAME_OF_ITEM, items_vs);  // Update the valueset for the current occurrence
    // endif;
PROC ITEM_ID
preproc
$ = curocc();


  
    valueset availableValueset;  // Temporary ValueSet to modify available items
    numeric i,selectedValue;                   // Counter variable

    availableValueset = NAME_OF_ITEM_VS1;  // Initialize with the original ValueSet

// if $ > max_allowed then 
	// errmsg("You have exceeded the max item allocation for this outlet")
	// select("Continue", continue, "Re-visit Item list", NAME_OF_ITEM(1)) default(1);
// endif;
postproc
if curocc() = 1 then 
	setvalueset(NAME_OF_ITEM, NAME_OF_ITEM_VS1);
 // Ensure we are in a later occurrence
    elseif curocc() > 1 then
        i = 1;
        while (i < curocc()) do
            selectedValue = NAME_OF_ITEM(i);  // Get previously selected value
            if selectedValue <> 0 and not selectedItems.seek(selectedValue) then
                selectedItems.add(selectedValue);  // Store unique selected item in list
            endif;
            i = i + 1;
        enddo;

        // Loop through the selected items and remove them from the ValueSet
        i = 1;
        while (i <= selectedItems.length()) do
            selectedValue = selectedItems(i);
            if invalueset(selectedValue, availableValueset) then
                availableValueset.remove(selectedValue);  // Remove from ValueSet if it exists
            endif;
            i = i + 1;
        enddo;

        // Apply the modified ValueSet to the next occurrence
        setvalueset(NAME_OF_ITEM, availableValueset);
    endif;
PROC NAME_OF_ITEM


//set item code and description
postproc
DISCRIPTION_OF_ITEM = $;
COICOP06_ITEM_CODE = $;
UOM = $;

//do mapping for unit of measurement 
// //set case label for easy identification
// setcaselabel(CPI_CODEBOOK_DICT, maketext("EM: %s  | ITN: %s | IC: %s", 
				// getvaluelabel(ENUMERATION_MARKET_EM),
				// getvaluelabel(NAME_OF_ITEM),
				// getvaluelabel(COICOP06_ITEM_CODE)
				// ));
 

	
  


PROC UOM

// preproc

// $ = NAME_OF_ITEM;

// protect($, true);
PROC QTY202101
if $ = 0 then 
	errmsg("Invalid Qauntity Range, please correct!");
	reenter;

endif;
PROC PRICE202101
if $ = 0 then
	errmsg("Invalid price range, please correct!");
	reenter;

endif;
PROC RCHECK



Onfocus
	capi_items_name_price = "<table><tbody><tr><th>Item</th><th>Price</th></tr>";
	// capi_number_of_items=curocc();
	do numeric ctr = 1 while ctr <= curocc()
		capi_items_name_price = capi_items_name_price + maketext("<tr><td>%s</td><td>%v</td></tr>", 
			getvaluelabel (NAME_OF_ITEM(ctr)), PRICE202101(ctr));
	enddo;

	capi_items_name_price = capi_items_name_price + "</tbody></table>";

//need check the current occ();
postproc
if $ = 2 then 
	//check
	endgroup;
	
	// elseif $ = 1 then
		// if ITEM_ID = max_allowed then
		
			// errmsg("You have exceeded the max item allocation for this outlet")
			// select("Continue", TYPE_OF_OUTLET, "Re-visit Item list", reenter) default(1);
		// endif;
	
endif;
PROC TYPE_OF_OUTLET
preproc

// list string respondent_query; 

// do numeric counter = 1 while counter <= count(NAME_OF_ITEM) 
    // respondent_query.add(NAME_OF_ITEM(counter)); 
// enddo; 

// numeric respondent_index = respondent_query.show("Who in the household is responding to questions?");

PROC PHONE_NUMBER_OF_OUTLET


checkPhone($);
PROC ND_PHONE_NUMBER_OF_OUTLET

checkPhone($);

PROC EMAIL_ADDRESS_OF_OUTLET

emailCheck($);
PROC STREET_ADDRESS_OF_OUTLET


//check if empty then throw a hard error

if $ = "" then
	errmsg("Please input an address for this outlet");
	reenter;

endif;
PROC CONTACT_PERSON_NAME

CheckName($);
PROC CONTACT_PERSON_EMAIL

emailCheck($);
PROC OTHER_SPECIFY_POSITION

preproc
ask if POSITION = 3;
PROC PHONE_NUMBER_1

checkPhone($);
PROC PHONE_NUMBER_2

// postproc
// if $ <> ""  then
// checkPhone($);
	// else
	// move;
// endif;
// PROC GPS_READING


// onfocus

	// if IsCompleteGPSData() then

		// // Update value set
		// if getos() = 20 then
			// setvalueset(GPS_READING, GPS_READING_VS2);
		// else
			// setvalueset(GPS_READING, GPS_READING_VS2);
		// endif;

		// // Update CAPI Text
		// capi_gps_data_status = "Collected";
		// capi_gps_accuracy_status_label = "GPS accuracy: ";
		// if IsDesiredAccuracy() then
			// capi_gps_accuracy_status = "Pass";

		// else
			// capi_gps_accuracy_status = "Fail";

		// endif;

		// // Prefill "keep data" so field can be advanced through when returning from partial save
		// GPS_READING = 2;

	// else

		// // Update value set
		
		// if getos() = 20 then
			// setvalueset(GPS_READING, GPS_READING_VS1);
		// else
			// setvalueset(GPS_READING, GPS_READING_VS1);
			
		// endif;

		// // Update CAPI Text
		// capi_gps_data_status = "not collected";
		// capi_gps_accuracy_status_label = "";
		// capi_gps_accuracy_status = "";

	// endif;


// postproc
	// // Handle user input
	// if GPS_READING = 1 then
		// TakeGPSReading();
		// // ISCOMPLETE = 1;
		

	// elseif GPS_READING = 2 then
		
		// capi_gps_reading = "Take GPS reading?";
	// elseif GPS_READING = 3 then
		// if IsCompleteGPSData() then
			// warning("GPS data will be deleted.")
				// select("Confirm", continue, "Cancel", GPS_READING);
		// endif;

		// DeleteGPSData();
		// capi_gps_reading = "Take GPS reading?";
		// reenter;
	// elseif GPS_READING = 4 then
		// // showMap(H12_GPS_LATITUDE,H12_GPS_LONGITUDE);
	// else
		// warning("Unhandled value set option.");
		// reenter;

	// endif;
PROC INTERVIEW_END_TIME
preproc
if demode() = 1 then 
	$ = edit("99:99:99", systime("HHMMSS"));
//	ISCOMPLETE = 1;
	else
endif;
