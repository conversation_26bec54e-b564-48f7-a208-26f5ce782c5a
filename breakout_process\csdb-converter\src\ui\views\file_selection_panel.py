"""
File selection panel
"""
import os
import logging
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QListWidget, QListWidgetItem, QGroupBox, QFileDialog
)
from PyQt6.QtCore import Qt, pyqtSlot

from src.ui.viewmodels.file_viewmodel import FileViewModel
from src.ui.resources.resource_manager import ResourceManager
from src.ui.views.file_preview_dialog import FilePreviewDialog

logger = logging.getLogger(__name__)

class FileSelectionPanel(QWidget):
    """File selection panel"""
    def __init__(self, view_model: FileViewModel):
        super().__init__()

        self.view_model = view_model

        # Initialize UI
        self._init_ui()

        # Connect signals
        self._connect_signals()

        # Update UI
        self._update_ui()

    def _init_ui(self):
        """Initialize UI components"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(10)

        # Create group box
        group_box = QGroupBox("CSDB Files")
        main_layout.addWidget(group_box)

        # Create group layout
        group_layout = QVBoxLayout(group_box)

        # Create drop zone label
        self.drop_label = QLabel("Drag and drop CSDB files here or use the Browse button")
        self.drop_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.drop_label.setStyleSheet(
            "border: 2px dashed #aaa; border-radius: 5px; padding: 20px;"
        )

        # Add file icon to drop zone
        file_icon = ResourceManager.get_icon("file.png")
        if not file_icon.isNull():
            self.drop_label.setPixmap(file_icon.pixmap(48, 48))
            self.drop_label.setText("\n\nDrag and drop CSDB files here\nor use the Browse button")

        group_layout.addWidget(self.drop_label)

        # Create file list
        self.file_list = QListWidget()
        self.file_list.setAlternatingRowColors(True)
        group_layout.addWidget(self.file_list)

        # Create button layout
        button_layout = QHBoxLayout()
        group_layout.addLayout(button_layout)

        # Create buttons
        self.browse_button = QPushButton("Browse...")
        self.browse_button.setIcon(ResourceManager.get_icon("browse.png"))

        self.preview_button = QPushButton("Preview")
        self.preview_button.setIcon(ResourceManager.get_icon("file.png"))

        self.remove_button = QPushButton("Remove")
        self.remove_button.setIcon(ResourceManager.get_icon("delete.png"))

        self.clear_button = QPushButton("Clear All")
        self.clear_button.setIcon(ResourceManager.get_icon("clear.png"))

        button_layout.addWidget(self.browse_button)
        button_layout.addWidget(self.preview_button)
        button_layout.addWidget(self.remove_button)
        button_layout.addWidget(self.clear_button)

        # Set initial button states
        self.preview_button.setEnabled(False)
        self.remove_button.setEnabled(False)
        self.clear_button.setEnabled(False)

        # Enable drag and drop
        self.setAcceptDrops(True)

    def _connect_signals(self):
        """Connect signals to slots"""
        # Connect view model signals
        self.view_model.files_changed.connect(self._update_file_list)

        # Connect UI signals
        self.browse_button.clicked.connect(self._on_browse)
        self.preview_button.clicked.connect(self._on_preview)
        self.remove_button.clicked.connect(self._on_remove)
        self.clear_button.clicked.connect(self._on_clear)
        self.file_list.itemSelectionChanged.connect(self._on_selection_changed)

    def _update_ui(self):
        """Update UI state"""
        self._update_file_list()

    @pyqtSlot()
    def _update_file_list(self):
        """Update file list"""
        self.file_list.clear()

        for file_path in self.view_model.files:
            item = QListWidgetItem(os.path.basename(file_path))
            item.setToolTip(file_path)
            item.setData(Qt.ItemDataRole.UserRole, file_path)
            self.file_list.addItem(item)

        # Update button states
        self.clear_button.setEnabled(len(self.view_model.files) > 0)

    def _on_selection_changed(self):
        """Handle selection change in file list"""
        selected = len(self.file_list.selectedItems()) > 0
        self.preview_button.setEnabled(selected)
        self.remove_button.setEnabled(selected)

    def _on_browse(self):
        """Handle browse button click"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "Open CSDB Files",
            "",
            "CSPro Database Files (*.csdb);;All Files (*.*)"
        )

        if file_paths:
            self.view_model.add_files(file_paths)

    def _on_remove(self):
        """Handle remove button click"""
        selected_items = self.file_list.selectedItems()
        if not selected_items:
            return

        file_paths = [item.data(Qt.ItemDataRole.UserRole) for item in selected_items]
        self.view_model.remove_files(file_paths)

    def _on_clear(self):
        """Handle clear button click"""
        self.view_model.clear_files()

    def _on_preview(self):
        """Handle preview button click"""
        # Get selected file
        selected_items = self.file_list.selectedItems()
        if not selected_items:
            return

        # Get file path
        file_path = selected_items[0].data(Qt.ItemDataRole.UserRole)

        # Create and show preview dialog
        dialog = FilePreviewDialog(self, file_path)
        dialog.exec()

    def dragEnterEvent(self, event):
        """Handle drag enter event"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event):
        """Handle drop event"""
        if event.mimeData().hasUrls():
            file_paths = []
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                if os.path.isfile(file_path) and file_path.lower().endswith('.csdb'):
                    file_paths.append(file_path)

            if file_paths:
                self.view_model.add_files(file_paths)

            event.acceptProposedAction()
