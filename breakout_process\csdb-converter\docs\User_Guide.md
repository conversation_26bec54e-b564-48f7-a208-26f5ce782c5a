# CSPro Database Converter - User Guide

## Introduction

The CSPro Database Converter is a tool designed to help you convert CSPro survey databases (CSDB) to modern SQL database systems. This guide will walk you through the process of using the application.

## Getting Started

### Installation

1. Ensure you have Python 3.8 or higher installed on your system.
2. Install the required dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the application:
   ```bash
   python main.py
   ```

### Main Interface

The application interface is divided into several sections:

1. **Connection Panel** (left side): Manage database connections
2. **File Selection Area** (top right): Select CSDB files for conversion
3. **Configuration Panel** (middle right): Configure conversion settings
4. **Progress and Status Area** (bottom right): Monitor conversion progress

## Step-by-Step Guide

### Step 1: Set Up Database Connection

1. Click the **Add** button in the Connection Panel.
2. Enter a name for your connection profile.
3. Select the database type (MySQL, PostgreSQL, SQL Server, or SQLite).
4. Enter the connection details:
   - Host (server address)
   - Port
   - Database name
   - Username
   - Password
5. Click **Test Connection** to verify the connection works.
6. Click **OK** to save the connection profile.
7. Select the profile in the list and click **Connect** to establish the connection.

### Step 2: Select CSDB Files

1. Click the **Browse** button in the File Selection Area.
2. Navigate to and select one or more CSDB files.
3. Alternatively, drag and drop CSDB files directly into the application.
4. Selected files will appear in the list.

### Step 3: Configure Conversion Settings

1. In the Configuration Panel, set the following options:
   - **Table Prefix**: Optional prefix for table names
   - **Batch Size**: Number of records to process in each batch
   - **Character Encoding**: Encoding used in the CSDB files
   - **Data Type Mapping**: How CSPro data types should be mapped to SQL types

### Step 4: Start Conversion

1. Click the **Start Conversion** button in the Progress and Status Area.
2. The conversion process will begin, and you can monitor progress in real-time.
3. You can pause, resume, or cancel the conversion if needed.
4. When complete, a success message will be displayed.

## Advanced Features

### Managing Connection Profiles

- **Edit Profile**: Select a profile and click **Edit** to modify its settings.
- **Delete Profile**: Select a profile and click **Delete** to remove it.
- **Recent Files**: Access recently used files from the File menu.

### Conversion Options

- **Table Prefix**: Use this to avoid table name conflicts in the database.
- **Batch Size**: Adjust for performance (smaller for less memory usage, larger for faster processing).
- **Data Type Mapping**: Customize how CSPro data types are converted to SQL data types.

## Troubleshooting

### Common Issues

1. **Connection Failed**: 
   - Verify the connection details are correct
   - Ensure the database server is running
   - Check if your user has the necessary permissions

2. **Invalid CSDB File**:
   - Ensure the file is a valid CSPro database file
   - Check if the file is corrupted

3. **Conversion Error**:
   - Check the error message in the status area
   - Verify the database has enough space
   - Ensure you have write permissions to the database

### Getting Help

If you encounter issues not covered in this guide, please:
1. Check the log files in the `.csdb-converter/logs` directory in your home folder
2. Report the issue with the log file attached

## Conclusion

The CSPro Database Converter simplifies the process of migrating your survey data to modern database systems. By following this guide, you should be able to successfully convert your CSDB files to SQL databases.
