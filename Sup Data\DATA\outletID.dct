infix dictionary using "C:\Users\<USER>\Desktop\CPI_CODEBOOK\Sup Data\DATA\outletID.dat" {
1 lines
    str      outlet_uuid    1:   1-12  
    byte     r_type      1:  13-13  
    byte     enumeration_market_em    1:  14-15  
    str      REC_TYPE    1:  16-16  
    int      item_id     1:  17-19  
    int      name_of_item    1:  20-23  
    int      discription_of_item    1:  24-26  
    byte     relevance    1:  27-27  
    byte     imp_local    1:  28-28  
    int      coicop06_item_code    1:  29-31  
    int      uom         1:  32-34  
    long     qty202101    1:  35-41  
    long     price202101    1:  42-48  
    byte     rcheck      1:  49-49  
}
