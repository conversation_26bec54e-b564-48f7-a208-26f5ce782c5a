"""
Conversion configuration panel
"""
import logging
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QSpinBox, QComboBox, QTabWidget, QFormLayout, QGroupBox
)
from PyQt6.QtCore import Qt, pyqtSlot

from src.ui.viewmodels.config_viewmodel import ConfigViewModel

logger = logging.getLogger(__name__)

class ConfigurationPanel(QWidget):
    """Conversion configuration panel"""
    def __init__(self, view_model: ConfigViewModel):
        super().__init__()
        
        self.view_model = view_model
        
        # Initialize UI
        self._init_ui()
        
        # Connect signals
        self._connect_signals()
        
        # Update UI
        self._update_ui()
    
    def _init_ui(self):
        """Initialize UI components"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(10)
        
        # Create group box
        group_box = QGroupBox("Conversion Configuration")
        main_layout.addWidget(group_box)
        
        # Create group layout
        group_layout = QVBoxLayout(group_box)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        group_layout.addWidget(self.tab_widget)
        
        # Create general settings tab
        general_tab = QWidget()
        self.tab_widget.addTab(general_tab, "General Settings")
        
        # Create general settings layout
        general_layout = QFormLayout(general_tab)
        
        # Table prefix
        self.prefix_edit = QLineEdit()
        general_layout.addRow("Table Prefix:", self.prefix_edit)
        
        # Batch size
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(100, 10000)
        self.batch_size_spin.setSingleStep(100)
        self.batch_size_spin.setValue(1000)
        general_layout.addRow("Batch Size:", self.batch_size_spin)
        
        # Character encoding
        self.encoding_combo = QComboBox()
        self.encoding_combo.addItems(["utf-8", "latin-1", "ascii", "cp1252"])
        general_layout.addRow("Character Encoding:", self.encoding_combo)
        
        # Max concurrent conversions
        self.concurrent_spin = QSpinBox()
        self.concurrent_spin.setRange(1, 10)
        self.concurrent_spin.setValue(3)
        general_layout.addRow("Max Concurrent Conversions:", self.concurrent_spin)
        
        # Create data type mapping tab
        mapping_tab = QWidget()
        self.tab_widget.addTab(mapping_tab, "Data Type Mapping")
        
        # Create mapping layout
        mapping_layout = QFormLayout(mapping_tab)
        
        # Numeric type mapping
        self.numeric_combo = QComboBox()
        self.numeric_combo.addItems(["INTEGER", "BIGINT", "DECIMAL"])
        mapping_layout.addRow("Numeric Type:", self.numeric_combo)
        
        # Alpha type mapping
        self.alpha_combo = QComboBox()
        self.alpha_combo.addItems(["VARCHAR", "TEXT", "CHAR"])
        mapping_layout.addRow("Alpha Type:", self.alpha_combo)
        
        # Date type mapping
        self.date_combo = QComboBox()
        self.date_combo.addItems(["DATE", "DATETIME", "VARCHAR"])
        mapping_layout.addRow("Date Type:", self.date_combo)
    
    def _connect_signals(self):
        """Connect signals to slots"""
        # Connect view model signals
        self.view_model.config_changed.connect(self._update_ui)
        
        # Connect UI signals
        self.prefix_edit.textChanged.connect(self.view_model.set_table_prefix)
        self.batch_size_spin.valueChanged.connect(self.view_model.set_batch_size)
        self.encoding_combo.currentTextChanged.connect(self.view_model.set_encoding)
        self.concurrent_spin.valueChanged.connect(self.view_model.set_max_concurrent)
        
        # Data type mapping signals
        self.numeric_combo.currentTextChanged.connect(
            lambda text: self.view_model.set_type_mapping("numeric", text)
        )
        self.alpha_combo.currentTextChanged.connect(
            lambda text: self.view_model.set_type_mapping("alpha", text)
        )
        self.date_combo.currentTextChanged.connect(
            lambda text: self.view_model.set_type_mapping("date", text)
        )
    
    @pyqtSlot()
    def _update_ui(self):
        """Update UI with current configuration"""
        # Block signals to prevent feedback loops
        self.prefix_edit.blockSignals(True)
        self.batch_size_spin.blockSignals(True)
        self.encoding_combo.blockSignals(True)
        self.concurrent_spin.blockSignals(True)
        self.numeric_combo.blockSignals(True)
        self.alpha_combo.blockSignals(True)
        self.date_combo.blockSignals(True)
        
        # Update UI elements
        self.prefix_edit.setText(self.view_model.table_prefix)
        self.batch_size_spin.setValue(self.view_model.batch_size)
        
        # Set encoding combo
        index = self.encoding_combo.findText(self.view_model.encoding)
        if index >= 0:
            self.encoding_combo.setCurrentIndex(index)
        
        self.concurrent_spin.setValue(self.view_model.max_concurrent)
        
        # Set data type mapping combos
        numeric_type = self.view_model.get_type_mapping("numeric")
        index = self.numeric_combo.findText(numeric_type)
        if index >= 0:
            self.numeric_combo.setCurrentIndex(index)
        
        alpha_type = self.view_model.get_type_mapping("alpha")
        index = self.alpha_combo.findText(alpha_type)
        if index >= 0:
            self.alpha_combo.setCurrentIndex(index)
        
        date_type = self.view_model.get_type_mapping("date")
        index = self.date_combo.findText(date_type)
        if index >= 0:
            self.date_combo.setCurrentIndex(index)
        
        # Unblock signals
        self.prefix_edit.blockSignals(False)
        self.batch_size_spin.blockSignals(False)
        self.encoding_combo.blockSignals(False)
        self.concurrent_spin.blockSignals(False)
        self.numeric_combo.blockSignals(False)
        self.alpha_combo.blockSignals(False)
        self.date_combo.blockSignals(False)
