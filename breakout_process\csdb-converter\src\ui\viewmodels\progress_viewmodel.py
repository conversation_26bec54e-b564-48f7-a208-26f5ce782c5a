"""
Progress view model
"""
import logging
from PyQt6.QtCore import QObject, pyqtSignal

from src.core.processors.migration_processor import MigrationStatus, MigrationProgress

logger = logging.getLogger(__name__)

class ProgressViewModel(QObject):
    """View model for progress and status"""

    # Signals
    progress_updated = pyqtSignal()
    status_changed = pyqtSignal(MigrationStatus)
    status_message = pyqtSignal(str)

    def __init__(self):
        super().__init__()

        # Progress values
        self._percentage = 0
        self._current_file = ""
        self._current_table = ""
        self._status = MigrationStatus.PENDING
        self._error_message = ""
        self._progress = MigrationProgress()
        self._records_per_second = 0
        self._estimated_time_remaining = 0

    @property
    def percentage(self) -> int:
        """Get progress percentage"""
        return self._percentage

    @property
    def current_file(self) -> str:
        """Get current file"""
        return self._current_file

    @property
    def current_table(self) -> str:
        """Get current table"""
        return self._current_table

    @property
    def status(self) -> MigrationStatus:
        """Get migration status"""
        return self._status

    @property
    def error_message(self) -> str:
        """Get error message"""
        return self._error_message

    @property
    def progress(self) -> MigrationProgress:
        """Get migration progress"""
        return self._progress

    @property
    def records_per_second(self) -> float:
        """Get processing speed in records per second"""
        return self._records_per_second

    @property
    def estimated_time_remaining(self) -> float:
        """Get estimated time remaining in seconds"""
        return self._estimated_time_remaining

    @property
    def is_running(self) -> bool:
        """Check if migration is running"""
        return self._status == MigrationStatus.IN_PROGRESS

    def update_progress(
        self,
        percentage: int,
        current_file: str,
        current_table: str,
        status: MigrationStatus,
        error_message: str = "",
        progress: MigrationProgress = None
    ) -> None:
        """Update progress values"""
        # Update values
        self._percentage = percentage
        self._current_file = current_file
        self._current_table = current_table

        # Update progress object if provided
        if progress:
            self._progress = progress
            self._records_per_second = progress.records_per_second
            self._estimated_time_remaining = progress.estimated_time_remaining

        # Check if status changed
        status_changed = self._status != status
        self._status = status

        # Set error message
        if error_message:
            self._error_message = error_message
            self.status_message.emit(f"Error: {error_message}")

        # Emit signals
        self.progress_updated.emit()

        if status_changed:
            self.status_changed.emit(status)

            # Emit status message
            if status == MigrationStatus.COMPLETED:
                self.status_message.emit("Migration completed successfully")
            elif status == MigrationStatus.FAILED:
                self.status_message.emit(f"Migration failed: {error_message}")
            elif status == MigrationStatus.CANCELLED:
                self.status_message.emit("Migration cancelled")
            elif status == MigrationStatus.PAUSED:
                self.status_message.emit("Migration paused")
            elif status == MigrationStatus.IN_PROGRESS:
                self.status_message.emit("Migration in progress")

    def reset(self) -> None:
        """Reset progress values"""
        self._percentage = 0
        self._current_file = ""
        self._current_table = ""
        self._status = MigrationStatus.PENDING
        self._error_message = ""
        self._progress = MigrationProgress()
        self._records_per_second = 0
        self._estimated_time_remaining = 0

        self.progress_updated.emit()
        self.status_changed.emit(MigrationStatus.PENDING)
        self.status_message.emit("Ready to start conversion")
