"""
Database connection panel
"""
import logging
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QListWidget, QListWidgetItem, QGroupBox, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSlot

from src.ui.views.connection_dialog import ConnectionDialog
from src.ui.viewmodels.connection_viewmodel import ConnectionViewModel
from src.core.database.connection import ConnectionStatus
from src.ui.resources.resource_manager import ResourceManager

logger = logging.getLogger(__name__)

class ConnectionPanel(QWidget):
    """Database connection panel"""
    def __init__(self, view_model: ConnectionViewModel):
        super().__init__()

        self.view_model = view_model

        # Initialize UI
        self._init_ui()

        # Connect signals
        self._connect_signals()

        # Update UI
        self._update_ui()

    def _init_ui(self):
        """Initialize UI components"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(10)

        # Create group box
        group_box = QGroupBox("Database Connections")
        main_layout.addWidget(group_box)

        # Create group layout
        group_layout = QVBoxLayout(group_box)

        # Create connection list
        self.connection_list = QListWidget()
        self.connection_list.setAlternatingRowColors(True)
        group_layout.addWidget(self.connection_list)

        # Create button layout
        button_layout = QHBoxLayout()
        group_layout.addLayout(button_layout)

        # Create buttons
        self.add_button = QPushButton("Add")
        self.add_button.setIcon(ResourceManager.get_icon("add.png"))

        self.edit_button = QPushButton("Edit")
        self.edit_button.setIcon(ResourceManager.get_icon("edit.png"))

        self.delete_button = QPushButton("Delete")
        self.delete_button.setIcon(ResourceManager.get_icon("delete.png"))

        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.delete_button)

        # Create connection status
        status_layout = QHBoxLayout()
        group_layout.addLayout(status_layout)

        status_layout.addWidget(QLabel("Status:"))
        self.status_label = QLabel("Disconnected")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        # Create connect button
        self.connect_button = QPushButton("Connect")
        self.connect_button.setIcon(ResourceManager.get_icon("connect.png"))
        group_layout.addWidget(self.connect_button)

        # Set initial button states
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        self.connect_button.setEnabled(False)

    def _connect_signals(self):
        """Connect signals to slots"""
        # Connect view model signals
        self.view_model.profiles_changed.connect(self._update_profile_list)
        self.view_model.connection_status_changed.connect(self._update_connection_status)

        # Connect UI signals
        self.add_button.clicked.connect(self._on_add_profile)
        self.edit_button.clicked.connect(self._on_edit_profile)
        self.delete_button.clicked.connect(self._on_delete_profile)
        self.connect_button.clicked.connect(self._on_connect)
        self.connection_list.itemSelectionChanged.connect(self._on_selection_changed)
        self.connection_list.itemDoubleClicked.connect(self._on_item_double_clicked)

    def _update_ui(self):
        """Update UI state"""
        self._update_profile_list()
        self._update_connection_status()

    @pyqtSlot()
    def _update_profile_list(self):
        """Update connection profile list"""
        self.connection_list.clear()

        for profile in self.view_model.profiles:
            item = QListWidgetItem(profile.name)
            item.setData(Qt.ItemDataRole.UserRole, profile.name)
            self.connection_list.addItem(item)

        # Select current profile if any
        if self.view_model.current_profile:
            for i in range(self.connection_list.count()):
                item = self.connection_list.item(i)
                if item.data(Qt.ItemDataRole.UserRole) == self.view_model.current_profile.name:
                    self.connection_list.setCurrentItem(item)
                    break

    @pyqtSlot()
    def _update_connection_status(self):
        """Update connection status display"""
        status = self.view_model.status

        if status == ConnectionStatus.DISCONNECTED:
            self.status_label.setText("Disconnected")
            self.status_label.setStyleSheet("color: gray;")
            self.connect_button.setText("Connect")
            self.connect_button.setIcon(ResourceManager.get_icon("connect.png"))
            self.connect_button.setEnabled(self.connection_list.currentItem() is not None)

        elif status == ConnectionStatus.CONNECTING:
            self.status_label.setText("Connecting...")
            self.status_label.setStyleSheet("color: orange;")
            self.connect_button.setEnabled(False)

        elif status == ConnectionStatus.CONNECTED:
            self.status_label.setText("Connected")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
            self.connect_button.setText("Disconnect")
            self.connect_button.setIcon(ResourceManager.get_icon("disconnect.png"))
            self.connect_button.setEnabled(True)

        elif status == ConnectionStatus.ERROR:
            self.status_label.setText("Error: " + self.view_model.error_message)
            self.status_label.setStyleSheet("color: red;")
            self.connect_button.setText("Connect")
            self.connect_button.setIcon(ResourceManager.get_icon("connect.png"))
            self.connect_button.setEnabled(self.connection_list.currentItem() is not None)

    def _on_selection_changed(self):
        """Handle selection change in connection list"""
        selected = self.connection_list.currentItem() is not None
        self.edit_button.setEnabled(selected)
        self.delete_button.setEnabled(selected)

        if self.view_model.status != ConnectionStatus.CONNECTING:
            self.connect_button.setEnabled(selected)

    def _on_item_double_clicked(self, item):
        """Handle double click on connection list item"""
        if self.view_model.status == ConnectionStatus.CONNECTED:
            # Already connected, disconnect first
            self.view_model.disconnect()

        # Connect to selected profile
        profile_name = item.data(Qt.ItemDataRole.UserRole)
        self.view_model.connect(profile_name)

    def _on_add_profile(self):
        """Handle add profile button click"""
        dialog = ConnectionDialog(self)
        if dialog.exec():
            # Add new profile
            self.view_model.add_profile(dialog.get_profile())

    def _on_edit_profile(self):
        """Handle edit profile button click"""
        item = self.connection_list.currentItem()
        if not item:
            return

        profile_name = item.data(Qt.ItemDataRole.UserRole)
        profile = self.view_model.get_profile(profile_name)

        if profile:
            dialog = ConnectionDialog(self, profile)
            if dialog.exec():
                # Update profile
                self.view_model.add_profile(dialog.get_profile())

    def _on_delete_profile(self):
        """Handle delete profile button click"""
        item = self.connection_list.currentItem()
        if not item:
            return

        profile_name = item.data(Qt.ItemDataRole.UserRole)

        # Confirm deletion
        result = QMessageBox.question(
            self,
            "Delete Profile",
            f"Are you sure you want to delete the profile '{profile_name}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if result == QMessageBox.StandardButton.Yes:
            # Delete profile
            self.view_model.remove_profile(profile_name)

    def _on_connect(self):
        """Handle connect button click"""
        if self.view_model.status == ConnectionStatus.CONNECTED:
            # Disconnect
            self.view_model.disconnect()
            return

        # Connect to selected profile
        item = self.connection_list.currentItem()
        if not item:
            return

        profile_name = item.data(Qt.ItemDataRole.UserRole)
        self.view_model.connect(profile_name)
