"""
Logging utility for the application
"""
import os
import logging
import datetime
from pathlib import Path

def setup_logger():
    """Configure application logging"""
    # Create logs directory if it doesn't exist
    logs_dir = Path.home() / ".csdb-converter" / "logs"
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Create log filename with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = logs_dir / f"converter_{timestamp}.log"
    
    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    # Set third-party loggers to WARNING level
    logging.getLogger("PyQt6").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
