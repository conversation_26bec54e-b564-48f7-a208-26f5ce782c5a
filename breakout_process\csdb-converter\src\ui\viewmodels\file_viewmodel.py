"""
File view model
"""
import os
import logging
from typing import List
from PyQt6.QtCore import QObject, pyqtSignal

from src.core.processors.csdb_processor import CSDBProcessor

logger = logging.getLogger(__name__)

class FileViewModel(QObject):
    """View model for file selection"""
    
    # Signals
    files_changed = pyqtSignal()
    validation_complete = pyqtSignal(str, bool)
    
    def __init__(self):
        super().__init__()
        
        # File list
        self._files: List[str] = []
        
        # Validation results
        self._validation_results = {}
    
    @property
    def files(self) -> List[str]:
        """Get list of selected files"""
        return self._files
    
    def add_file(self, file_path: str) -> None:
        """Add a file to the list"""
        # Check if file already exists in list
        if file_path in self._files:
            return
        
        # Add file
        self._files.append(file_path)
        self.files_changed.emit()
        logger.info(f"Added file: {file_path}")
        
        # Validate file
        self._validate_file(file_path)
    
    def add_files(self, file_paths: List[str]) -> None:
        """Add multiple files to the list"""
        added = False
        
        for file_path in file_paths:
            # Check if file already exists in list
            if file_path not in self._files:
                # Add file
                self._files.append(file_path)
                added = True
                logger.info(f"Added file: {file_path}")
                
                # Validate file
                self._validate_file(file_path)
        
        if added:
            self.files_changed.emit()
    
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the list"""
        if file_path in self._files:
            self._files.remove(file_path)
            
            # Remove validation result
            if file_path in self._validation_results:
                del self._validation_results[file_path]
            
            self.files_changed.emit()
            logger.info(f"Removed file: {file_path}")
    
    def remove_files(self, file_paths: List[str]) -> None:
        """Remove multiple files from the list"""
        removed = False
        
        for file_path in file_paths:
            if file_path in self._files:
                self._files.remove(file_path)
                
                # Remove validation result
                if file_path in self._validation_results:
                    del self._validation_results[file_path]
                
                removed = True
                logger.info(f"Removed file: {file_path}")
        
        if removed:
            self.files_changed.emit()
    
    def clear_files(self) -> None:
        """Clear all files"""
        if self._files:
            self._files.clear()
            self._validation_results.clear()
            self.files_changed.emit()
            logger.info("Cleared all files")
    
    def is_file_valid(self, file_path: str) -> bool:
        """Check if file is valid"""
        return self._validation_results.get(file_path, False)
    
    def are_all_files_valid(self) -> bool:
        """Check if all files are valid"""
        if not self._files:
            return False
        
        return all(self.is_file_valid(file_path) for file_path in self._files)
    
    def _validate_file(self, file_path: str) -> None:
        """Validate a CSDB file"""
        # Check if file exists
        if not os.path.isfile(file_path):
            self._validation_results[file_path] = False
            self.validation_complete.emit(file_path, False)
            logger.warning(f"File not found: {file_path}")
            return
        
        # Check file extension
        if not file_path.lower().endswith('.csdb'):
            self._validation_results[file_path] = False
            self.validation_complete.emit(file_path, False)
            logger.warning(f"Not a CSDB file: {file_path}")
            return
        
        try:
            # Create processor
            processor = CSDBProcessor(file_path)
            
            # Validate file
            valid = processor.validate()
            
            self._validation_results[file_path] = valid
            self.validation_complete.emit(file_path, valid)
            
            if valid:
                logger.info(f"File validated successfully: {file_path}")
            else:
                logger.warning(f"Invalid CSDB file: {file_path}")
                
        except Exception as e:
            self._validation_results[file_path] = False
            self.validation_complete.emit(file_path, False)
            logger.error(f"Error validating file: {file_path} - {e}")
