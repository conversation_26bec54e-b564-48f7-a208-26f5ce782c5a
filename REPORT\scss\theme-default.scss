@import './_components/include';
@import './_theme/common';
@import './_theme/_theme';

$primary-color: #696cff;

@include template-common-theme($primary-color);

// Navbar
// ---------------------------------------------------------------------------
@include template-navbar-style('.bg-navbar-theme', $card-bg, $color: $body-color, $active-color: $headings-color);
.layout-navbar {
  background-color: rgba($card-bg, 0.95) !important;
  backdrop-filter: saturate(200%) blur(6px);
}
.navbar-detached {
  box-shadow: 0 0 0.375rem 0.25rem rgba(rgba-to-hex($gray-500, $rgba-to-hex-bg), 0.15);
}
.layout-navbar-fixed .layout-page:before {
  backdrop-filter: saturate(200%) blur(10px);
  background: rgba($body-bg, 0.6);
}

// Menu
// ---------------------------------------------------------------------------
@include template-menu-style(
  '.bg-menu-theme',
  $white,
  $color: $body-color,
  $active-color: $headings-color,
  $border: transparent,
  $active-bg: $white
);

.bg-menu-theme {
  // Menu header
  .menu-header {
    color: $text-muted;
    &:before {
      background-color: $text-muted !important;
    }
  }

  // menu shadow
  &.menu-vertical {
    box-shadow: 0 0.125rem 0.375rem 0 rgba(rgba-to-hex($gray-500, $rgba-to-hex-bg), 0.12);
  }

  // menu item open(vertical) & hover (vertical & horizontal)
  .menu-inner .menu-item.open > .menu-link,
  .menu-inner .menu-item .menu-link:not(.active):hover {
    html:not(.layout-menu-collapsed) &,
    .layout-menu-hover.layout-menu-collapsed & {
      background-color: rgba($black, 0.04);
    }
  }
  // menu item open(horizontal)
  .menu-inner .menu-sub > .menu-item.active {
    > .menu-link.menu-toggle {
      background-color: rgba($black, 0.04);
    }
    .menu-icon {
      color: $primary-color;
    }
  }
  // menu item active
  .menu-inner > .menu-item.active > .menu-link {
    color: $primary-color;
    background-color: rgba($primary-color, 0.16) !important;
  }

  .menu-inner > .menu-item.active:before {
    background: $primary-color;
  }

  // Sub menu item link bullet
  .menu-sub > .menu-item > .menu-link:before {
    background-color: rgba-to-hex($gray-400, $rgba-to-hex-bg) !important;
  }
  // Sub menu item link active bullet
  .menu-sub > .menu-item.active > .menu-link:not(.menu-toggle):before {
    background-color: $primary-color !important;
    border: 3px solid rgba-to-hex(rgba($primary-color, 0.16), $rgba-to-hex-bg) !important;
  }
}

.app-brand .layout-menu-toggle {
  background-color: $primary-color;
  border: 7px solid $body-bg;
  i {
    color: color-contrast($primary-color);
  }
  .menu-inner > .menu-header::before {
    background-color: rgba-to-hex($gray-400, $rgba-to-hex-bg);
  }
}

// Footer
// ---------------------------------------------------------------------------
@include template-footer-style('.bg-footer-theme', $body-bg, $color: $body-color, $active-color: $headings-color);

.layout-footer-fixed {
  .content-footer {
    box-shadow: 0 0 0.375rem 0.25rem rgba(rgba-to-hex($gray-500, $rgba-to-hex-bg), 0.15);
  }
}
