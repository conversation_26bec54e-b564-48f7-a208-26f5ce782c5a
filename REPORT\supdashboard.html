﻿<!DOCTYPE html>

<!-- =========================================================
* Sneat - Bootstrap 5 HTML Admin Template - Pro | v1.0.0
==============================================================

* Product Page: https://themeselection.com/products/sneat-bootstrap-html-admin-template/
* Created by: ThemeSelection
* License: You must have a valid license purchased in order to legally use the theme for your project.
* Copyright ThemeSelection (https://themeselection.com)

=========================================================
 -->
<!-- beautify ignore:start -->
<html
  lang="en"
  class="light-style layout-menu-fixed"
  dir="ltr"
  data-theme="theme-default"
  data-assets-path="../assets/"
  data-template="vertical-menu-template-free"
>
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0"
    />

    <title>Dashboard - Analytics | Supervisor</title>

    <meta name="description" content="" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="./assets/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap"
      rel="stylesheet"
    />

    <!-- Icons. Uncomment required icon fonts -->
    <link rel="stylesheet" href="./assets/vendor/fonts/boxicons.css" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="./assets/vendor/css/core.css" class="template-customizer-core-css" />
    <link rel="stylesheet" href="./assets/vendor/css/theme-default.css" class="template-customizer-theme-css" />
    <link rel="stylesheet" href="./assets/css/demo.css" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="./assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />

    <link rel="stylesheet" href="./assets/vendor/libs/apex-charts/apex-charts.css" />

    <!-- Page CSS -->

    <!-- Helpers -->
    <script src="./assets/vendor/js/helpers.js"></script>

    <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->
    <!--? Config:  Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file.  -->
    <script src="./assets/js/config.js"></script>
  </head>

  <body>
    <!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
      <div class="layout-container">
        <!-- Menu -->

        <!-- / Menu -->

        <!-- Layout container -->
        <div class="layout-page">
          <!-- Navbar -->
		  <? 
			numeric i=0;
		?>
		<?
			numeric Total_count = countcases(CPI_CODEBOOK_DICT);
			numeric partial_count = 0; 
			numeric completed_count = 0;//countcases(CPI_CODEBOOK_DICT where INTERVIEW_RESULT = 1);
			
			numeric tot_Refusal = 0;
			numeric	not_found = 0;
			numeric others = 0;
			
			string partialCase, zdate, status; 
			
			forcase CPI_CODEBOOK_DICT do 
				if  !ispartial(CPI_CODEBOOK_DICT) and INTRODUCTION = 1 then
				inc(completed_count); 
				endif;
			endfor; 

			forcase CPI_CODEBOOK_DICT do 
			
				if ispartial(CPI_CODEBOOK_DICT) then 
					inc(partial_count); 
					elseif INTERVIEW_RESULT = 3 then
					inc(tot_Refusal);
					elseif INTERVIEW_RESULT = 4 then
					inc(not_found);
					elseif INTERVIEW_RESULT = 98 then
					inc(others);
					//if ispartial(BPS24_DICT) = "" then
						//inc(complete_count);
					//endif;
				endif; 	
			
					
			endfor; 
			
		?>
          <!-- / Navbar -->
          <!-- Content wrapper -->
          <div class="content-wrapper">
            <!-- Content -->
            <div class="card-title mb-0">
              <div class="demo-inline-spacing">
                <a class="">
                  <h4 class="m-0 me-2 fw-bold p-4">Supervisor Dashboard 
                    <a>
					<a type="button" class="btn rounded-pill btn-dark mx-5 align-self-lg-end"
                    href="javascript:CSPro.runLogicAsync('syncWithInterviewer();');"
                    >Get Data</a>
					</a>
					</h4>
                </a>
               
              </div>
              <ul class="text-muted">Report generated at ~~timestring()~~</ul>
			            <div class="bs-toast toast fade" role="alert" aria-live="assertive" aria-atomic="true" id="myToast">
			            <div class="toast-header">
				         <i class="bx bx-bell me-2"></i>
					       <div class="me-auto fw-semibold">Notification</div>
					  	    <small>Just now</small>
					    	<button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
				    	</div>
					      <div class="toast-body">No data has been sent to the server.</div>
				      </div>
            </div>
            
            <div class="container-xxl flex-grow-1 container-p-y">
              <div class="row">
                <div class="col-lg-4 col-md-4 order-1">
                  <div class="row">
                    <div class="col-lg-6 col-md-12 col-6 mb-4">
                      <div class="card">
                        <div class="card-body">
                          <div class="card-title d-flex align-items-start justify-content-between">
                            <div class="avatar flex-shrink-0">
                              <img
                                src="./assets/img/icons/unicons/chart-success.png"
                                alt="chart success"
                                class="rounded"
                              />
                            </div>
                            <div class="dropdown">
                                <button
                                  class="btn p-0"
                                  type="button"
                                  id="cardOpt1"
                                  data-bs-toggle="dropdown"
                                  aria-haspopup="true"
                                  aria-expanded="false"
                                >
                                  <i class="bx bx-dots-vertical-rounded"></i>
                                </button>
                              </div>
                          </div>
                          <span class="fw-semibold d-block mb-1">Total</span>
                          <h2 class="card-title mb-2 text-success fw-semibold">~~Total_count~~ </h2>
                        </div>
                      </div>
                    </div>
                    <div class="col-lg-6 col-md-12 col-6 mb-4">
                      <div class="card">
                        <div class="card-body">
                          <div class="card-title d-flex align-items-start justify-content-between">
                            <div class="avatar flex-shrink-0">
                              <img
                                src="./assets/img/icons/unicons/data-file.png"
                                alt="Credit Card"
                                class="rounded"
                              />
                            </div>
                            <div class="dropdown">
                              <button
                                class="btn p-0"
                                type="button"
                                id="cardOpt6"
                                data-bs-toggle="dropdown"
                                aria-haspopup="true"
                                aria-expanded="false"
                              >
                                <i class="bx bx-dots-vertical-rounded"></i>
                              </button>
                              
                            </div>
                          </div>
                          <span>Partial</span>
                          <h1 class="card-title text-nowrap mb-1 text-warning fw-semibold">~~partial_count~~</h1>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-md-8 col-lg-4 order-3 order-md-2">
                  <div class="row">
                    <div class="col-6 mb-4">
                      <div class="card">
                        <div class="card-body">
                          <div class="card-title d-flex align-items-start justify-content-between">
                            <div class="avatar flex-shrink-0">
                              <img src="./assets/img/icons/unicons/database.png" alt="Credit Card" class="rounded" />
                            </div>
                            <div class="dropdown">
                              <button
                                class="btn p-0"
                                type="button"
                                id="cardOpt4"
                                data-bs-toggle="dropdown"
                                aria-haspopup="true"
                                aria-expanded="false"
                              >
                                <i class="bx bx-dots-vertical-rounded"></i>
                              </button>
                            </div>
                          </div>
                          <span class="d-block mb-1">Completed</span>
                          <h3 class="card-title text-nowrap mb-2 text-success fw-semibold">~~completed_count~~</h3>
                        </div>
                      </div>
                    </div>
                    <div class="col-6 mb-4">
                      <div class="card">
                        <div class="card-body">
                          <div class="card-title d-flex align-items-start justify-content-between">
                            <div class="avatar flex-shrink-0">
                              <img src="./assets/img/icons/unicons/refusal.png" alt="complete count" class="rounded" />
                            </div>
                            <div class="dropdown">
                              <button
                                class="btn p-0"
                                type="button"
                                id="cardOpt1"
                                data-bs-toggle="dropdown"
                                aria-haspopup="true"
                                aria-expanded="false"
                              >
                                <i class="bx bx-dots-vertical-rounded"></i>
                              </button>
                            </div>
                          </div>
                          <span class="fw-semibold d-block mb-1 ">Unavailable</span>
                          <h3 class="card-title mb-2 text-danger fw-semibold align-content-lg-end">~~tot_Refusal~~</h3>
                        </div>
                      </div>
                    </div>
                    
                    <!-- </div>
                  <div class="row"> -->
      
                    

                  </div>
                </div>
              </div>
              <div class="row">
                <!-- Order Statistics -->
                <div class="col-md-6 col-lg-4 col-xl-4 order-0 mb-4">
                  <div class="card h-100">
                    <div class="card-header d-flex align-items-center justify-content-between pb-0">
                      <div class="card-title mb-0">
                        <h5 class="m-0 me-2">Other Statistics</h5>
                        <!-- <small class="text-muted">42.82k Total Sales</small> -->
                      </div>
                      <div class="dropdown">
                        <button
                          class="btn p-0"
                          type="button"
                          id="orederStatistics"
                          data-bs-toggle="dropdown"
                          aria-haspopup="true"
                          aria-expanded="false"
                        >
                          <i class="bx bx-dots-vertical-rounded"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end" aria-labelledby="orederStatistics">
                          <a class="dropdown-item" href="javascript:void(0);">Refresh</a>
                          <a class="dropdown-item" href="javascript:void(0);">Share</a>
                        </div>
                      </div>
                    </div>
                    <div class="card-body">
                      <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex flex-column align-items-center gap-1">
                          <h2 class="mb-2">~~Total_count~~</h2>
                          <span>Total Cases</span>
                        </div>
                        <div id="orderStatisticsChartN"></div>
                      </div>
                      <ul class="p-0 m-0">
                        <li class="d-flex mb-4 pb-1">
                          
                          <div class="d-flex w-100 flex-wrap align-items-center justify-content-between gap-2">
                            <div class="me-2">
                              <h6 class="mb-0">Not Found</h6>
                              <!-- <small class="text-muted">
							  </small> -->
                            </div>
                            <div class="user-progress">
                              <small class="fw-semibold">~~not_found~~</small>
                            </div>
                          </div>
                        </li>

                        <li class="d-flex mb-4 pb-1">
                          <div class="d-flex w-100 flex-wrap align-items-center justify-content-between gap-2">
                            <div class="me-2">
                              <h6 class="mb-0">Partially completed</h6>
							  
                              <!-- <small class="text-muted">Mobile, Earbuds, TV</small> -->
                            </div>
                            <div class="user-progress">
                              <small class="fw-semibold">~~partial_count~~</small>
                            </div>
                          </div>
                        </li>

                        <li class="d-flex mb-4 pb-1">
                          
                          <div class="d-flex w-100 flex-wrap align-items-center justify-content-between gap-2">
                            <div class="me-2">
                              <h6 class="mb-0">Other Reasons</h6>
                              <!-- <small class="text-muted">Mobile, Earbuds, TV</small> -->
                            </div>
                            <div class="user-progress">
                              <small class="fw-semibold">~~others~~</small>
                            </div>
                          </div>
                        </li>
                        
                      </ul>
                    </div>
                  </div>
                </div>
                <!--/ Order Statistics -->
                <!-- Expense Overview -->
                <div class="col-md-6 col-lg-4 order-1 mb-4">
                  <div class="card h-100">
                    <div class="card-header">
                     <h6>Performance</h6>
                    </div>
                    <div class="card-body px-0">
                      <div class="tab-content p-0">
                        <div class="tab-pane fade show active" id="navs-tabs-line-card-income" role="tabpanel">
                          <div class="d-flex p-4 pt-3">
                            <div class="avatar flex-shrink-0 me-3">
                              <img src="./assets/img/icons/unicons/database.png" alt="User" />
                            </div>
                            <div>
                              <small class="text-muted d-block">Average Sync Cases and Time</small>
                            </div>
                          </div>
                          <div id="incomeChart"></div>
                          <div class="d-flex justify-content-center pt-4 gap-2">
                            <div class="flex-shrink-0">
                              
                            </div>
                            <div>
                              <small class="text-muted">Performance is measured against sync time from the server</small>
							  
                            </div>
							<div><span> <small class="text">This Module is still under development</small></span>
							</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!--/ Expense Overview -->
                <!-- Transactions -->
                <!--/ Transactions -->
              </div>
            </div>
            <!-- / Content -->
            <div class="card">
              <h5 class="card-header">Case Report</h5>
              <div class="table-responsive text-nowrap">
                <table class="table table-borderless">
                  <thead>
                    <tr>
                      <th>CaseID</th>
                      <th>Outlet Name</th>
                      <th>EM</th>
                      <th>Int Date</th>
                      <th>Status</th>
					  <th>Enumerator</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
			
				<? forcase CPI_CODEBOOK_DICT do 
					inc(i);
					agent_hd.add(key(CPI_CODEBOOK_DICT));
					if  ispartial(CPI_CODEBOOK_DICT) then
						partialCase = key(CPI_CODEBOOK_DICT);
						status = "partial";
						string enum = S_NAME;
					
					?>
                    <tr>
                      <td><i class="fab fa-angular fa-lg text-danger me-3"></i> <strong>~~Maketext(partialCase)~~</strong></td>
					  <td><i class="fab fa-angular fa-lg text-danger me-3"></i> <strong> ~~strip(NAME_OF_OUTLET)~~</strong></td>
                      <td>~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~</td>
                      <td>
                        ~~strip(INTERVIEW_START_DATE)~~
                      </td>
                      <td><span class="badge bg-label-warning me-1">~~strip(status)~~</span></td>
					  <td><span class="badge bg-label-danger me-1">~~strip(enum)~~</span></td>
                      <td>
                        <div class="dropdown">
                          <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                            <i class="bx bx-dots-vertical-rounded"></i>
                          </button>
                          <div class="dropdown-menu">
                            <a class="dropdown-item" href="javascript:CSPro.runLogicAsync('modify_case(~~i~~);');"
                              ><i class="bx bx-edit-alt me-1"></i>Edit</a>
                            
                            <a class="dropdown-item" href="javascript:void(0);"
                              ><i class="bx bx-trash me-1"></i>Discard</a>
                            
                          </div>
                        </div>
                      </td>
                    </tr>
                    <? endif;
					endfor; ?>
					
					<? forcase CPI_CODEBOOK_DICT do 
					 if  !ispartial(CPI_CODEBOOK_DICT) and INTERVIEW_RESULT = 3 then
						 partialCase = key(CPI_CODEBOOK_DICT);
						 status = "Unavailable";
						 string enum = INTERVIEWER_NAME;
					?>
                    <tr>
                      <td><i class="fab fa-angular fa-lg text-danger me-3"></i> <strong>~~Maketext("%s",partialCase)~~</strong></td>
					  <td><i class="fab fa-angular fa-lg text-danger me-3"></i> <strong> ~~strip(NAME_OF_OUTLET)~~</strong></td>
                      <td>~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~</td>
                      <td>
                        ~~strip(INTERVIEW_START_DATE)~~
                      </td>
                      <td><span class="badge bg-label-danger me-1">~~strip(status)~~</span></td>
					  <td><span class="badge bg-label-danger me-1">~~strip(enum)~~</span></td>
                      <td>
                        <div class="dropdown">
                          <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                            <i class="bx bx-dots-vertical-rounded"></i>
                          </button>
                          <div class="dropdown-menu">
                            <a class="dropdown-item" href="javascript:CSPro.runLogicAsync('modify_case(~~i~~);');"
                              ><i class="bx bx-edit-alt me-1"></i>Edit</a>
                            
                            <a class="dropdown-item" href="javascript:void(0);"
                              ><i class="bx bx-trash me-1"></i>Discard</a>
                            
                          </div>
                        </div>
                      </td>
                    </tr>
                    <? endif;
					endfor; ?>
					
					<? forcase CPI_CODEBOOK_DICT do 
					 if  INTERVIEW_RESULT = 4 then
						 partialCase = key(CPI_CODEBOOK_DICT);
						 status = "Not Found";
						 string enum = INTERVIEWER_NAME;
					?>
                    <tr>
                      <td><i class="fab fa-angular fa-lg text-danger me-3"></i> <strong>~~Maketext("%s",partialCase)~~</strong></td>
					  <td><i class="fab fa-angular fa-lg text-danger me-3"></i> <strong> ~~strip(NAME_OF_OUTLET)~~</strong></td>
                      <td>~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~</td>
                      <td>
                        ~~strip(INTERVIEW_START_DATE)~~
                      </td>
                      <td><span class="badge bg-label-danger me-1">~~strip(status)~~</span></td>
					  <td><span class="badge bg-label-danger me-1">~~strip(enum)~~</span></td>
                      <td>
                        <div class="dropdown">
                          <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                            <i class="bx bx-dots-vertical-rounded"></i>
                          </button>
                          <div class="dropdown-menu">
                            <a class="dropdown-item" href="javascript:CSPro.runLogicAsync('modify_case(~~i~~);');"
                              ><i class="bx bx-edit-alt me-1"></i>Edit</a>
                            
                            <a class="dropdown-item" href="javascript:CS());"
                              ><i class="bx bx-trash me-1"></i>Discard</a>
                            
                          </div>
                        </div>
                      </td>
                    </tr>
                    <? endif;
					endfor; ?>
                  </tbody>
                </table>
              </div>
            </div>
            <!--/ Borderless Table -->

            <!-- Footer -->
            <!-- / Footer -->

            <div class="content-backdrop fade"></div>
          </div>
          <!-- Content wrapper -->
        </div>
        <!-- / Layout page -->
      </div>

      <!-- Overlay -->
      <div class="layout-overlay layout-menu-toggle"></div>
    </div>
    <!-- / Layout wrapper -->

    <!-- Core JS -->
    <!-- build:js assets/vendor/js/core.js -->
    <script src="./assets/vendor/libs/jquery/jquery.js"></script>
    <script src="./assets/vendor/libs/popper/popper.js"></script>
    <script src="./assets/vendor/js/bootstrap.js"></script>
    <script src="./assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js"></script>

    <script src="./assets/vendor/js/menu.js"></script>
    <!-- endbuild -->

    <!-- Vendors JS -->
    <script src="./assets/vendor/libs/apex-charts/apexcharts.js"></script>

    <!-- Main JS -->
    <script src="./assets/js/main.js"></script>

    <!-- Page JS -->
    <script src="./assets/js/dashboards-analytics.js"></script>

    <!-- Place this tag in your head or just before your close body tag. -->
    <script async defer src="https://buttons.github.io/buttons.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"></script>

     
	
  </body>
</html>

