"""
Database connection dialog
"""
import logging
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QComboBox, QFormLayout, QDialogButtonBox,
    QCheckBox, QSpinBox, QMessageBox
)
from PyQt6.QtCore import Qt

from src.core.database.connection import ConnectionProfile, DatabaseType
from src.ui.resources.resource_manager import ResourceManager

logger = logging.getLogger(__name__)

class ConnectionDialog(QDialog):
    """Dialog for creating/editing database connection profiles"""
    def __init__(self, parent=None, profile=None):
        super().__init__(parent)

        self.profile = profile
        self.is_edit_mode = profile is not None

        # Set dialog properties
        self.setWindowTitle("Connection Profile")
        self.setMinimumWidth(400)

        # Initialize UI
        self._init_ui()

        # Fill form if editing
        if self.is_edit_mode:
            self._fill_form()

    def _init_ui(self):
        """Initialize UI components"""
        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create form layout
        form_layout = QFormLayout()
        main_layout.addLayout(form_layout)

        # Profile name
        self.name_edit = QLineEdit()
        form_layout.addRow("Profile Name:", self.name_edit)

        # Database type
        self.db_type_combo = QComboBox()
        for db_type in DatabaseType:
            self.db_type_combo.addItem(DatabaseType.get_display_name(db_type), db_type)
        form_layout.addRow("Database Type:", self.db_type_combo)

        # Host
        self.host_edit = QLineEdit()
        form_layout.addRow("Host:", self.host_edit)

        # Port
        self.port_spin = QSpinBox()
        self.port_spin.setRange(0, 65535)
        self.port_spin.setValue(0)
        form_layout.addRow("Port:", self.port_spin)

        # Database name
        self.database_edit = QLineEdit()
        form_layout.addRow("Database:", self.database_edit)

        # Username
        self.username_edit = QLineEdit()
        form_layout.addRow("Username:", self.username_edit)

        # Password
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addRow("Password:", self.password_edit)

        # Connection string
        self.conn_string_check = QCheckBox("Use custom connection string")
        form_layout.addRow("", self.conn_string_check)

        self.conn_string_edit = QLineEdit()
        self.conn_string_edit.setEnabled(False)
        form_layout.addRow("Connection String:", self.conn_string_edit)

        # Test connection button
        self.test_button = QPushButton("Test Connection")
        self.test_button.setIcon(ResourceManager.get_icon("database.png"))
        form_layout.addRow("", self.test_button)

        # Dialog buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        main_layout.addWidget(button_box)

        # Connect signals
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        self.conn_string_check.toggled.connect(self._on_conn_string_toggled)
        self.db_type_combo.currentIndexChanged.connect(self._on_db_type_changed)
        self.test_button.clicked.connect(self._on_test_connection)

        # Set initial state
        self._on_db_type_changed()

    def _fill_form(self):
        """Fill form with profile data"""
        self.name_edit.setText(self.profile.name)

        # Set database type
        index = self.db_type_combo.findData(self.profile.db_type)
        if index >= 0:
            self.db_type_combo.setCurrentIndex(index)

        self.host_edit.setText(self.profile.host)
        self.port_spin.setValue(self.profile.port)
        self.database_edit.setText(self.profile.database)
        self.username_edit.setText(self.profile.username)
        self.password_edit.setText(self.profile.password)

        # Set connection string if any
        if self.profile.connection_string:
            self.conn_string_check.setChecked(True)
            self.conn_string_edit.setText(self.profile.connection_string)

    def _on_conn_string_toggled(self, checked):
        """Handle connection string checkbox toggle"""
        self.conn_string_edit.setEnabled(checked)

        # Enable/disable other fields
        self.host_edit.setEnabled(not checked)
        self.port_spin.setEnabled(not checked)
        self.database_edit.setEnabled(not checked)
        self.username_edit.setEnabled(not checked)
        self.password_edit.setEnabled(not checked)

    def _on_db_type_changed(self):
        """Handle database type change"""
        db_type = self.db_type_combo.currentData()

        # Set default port based on database type
        if db_type == DatabaseType.MYSQL:
            self.port_spin.setValue(3306)
        elif db_type == DatabaseType.POSTGRESQL:
            self.port_spin.setValue(5432)
        elif db_type == DatabaseType.SQLSERVER:
            self.port_spin.setValue(1433)
        elif db_type == DatabaseType.SQLITE:
            # SQLite doesn't use host/port
            self.host_edit.setEnabled(False)
            self.port_spin.setEnabled(False)
            self.port_spin.setValue(0)
            return

        # Enable host/port for other database types
        self.host_edit.setEnabled(True)
        self.port_spin.setEnabled(True)

    def _on_test_connection(self):
        """Handle test connection button click"""
        # Create profile from form data
        profile = self.get_profile()

        # Validate form before testing
        if not self._validate_form():
            return

        # Update button state
        self.test_button.setText("Testing...")
        self.test_button.setEnabled(False)

        # Test connection
        from src.core.database.connection import ConnectionManager
        manager = ConnectionManager()

        success = manager.test_connection(profile)

        # Reset button state
        self.test_button.setText("Test Connection")
        self.test_button.setEnabled(True)

        if success:
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("Connection Test")
            msg_box.setText("Connection successful!")
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setDefaultButton(QMessageBox.StandardButton.Ok)
            msg_box.exec()
        else:
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("Connection Test")
            msg_box.setText("Connection failed")
            msg_box.setDetailedText(manager.error_message)
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setDefaultButton(QMessageBox.StandardButton.Ok)
            msg_box.exec()

    def get_profile(self) -> ConnectionProfile:
        """Get connection profile from form data"""
        name = self.name_edit.text()
        db_type = self.db_type_combo.currentData()

        if self.conn_string_check.isChecked():
            # Use custom connection string
            return ConnectionProfile(
                name=name,
                db_type=db_type,
                connection_string=self.conn_string_edit.text()
            )
        else:
            # Use individual fields
            return ConnectionProfile(
                name=name,
                db_type=db_type,
                host=self.host_edit.text(),
                port=self.port_spin.value(),
                database=self.database_edit.text(),
                username=self.username_edit.text(),
                password=self.password_edit.text()
            )

    def _validate_form(self) -> bool:
        """Validate form data"""
        if not self.name_edit.text():
            QMessageBox.warning(
                self,
                "Validation Error",
                "Profile name is required."
            )
            self.name_edit.setFocus()
            return False

        if self.conn_string_check.isChecked() and not self.conn_string_edit.text():
            QMessageBox.warning(
                self,
                "Validation Error",
                "Connection string is required."
            )
            self.conn_string_edit.setFocus()
            return False

        if not self.conn_string_check.isChecked():
            # Validate required fields based on database type
            db_type = self.db_type_combo.currentData()

            if db_type != DatabaseType.SQLITE:
                if not self.host_edit.text():
                    QMessageBox.warning(
                        self,
                        "Validation Error",
                        "Host is required."
                    )
                    self.host_edit.setFocus()
                    return False

            if not self.database_edit.text():
                QMessageBox.warning(
                    self,
                    "Validation Error",
                    "Database name is required."
                )
                self.database_edit.setFocus()
                return False

        return True

    def accept(self):
        """Handle dialog acceptance"""
        # Validate form
        if not self._validate_form():
            return

        # Accept dialog
        super().accept()
