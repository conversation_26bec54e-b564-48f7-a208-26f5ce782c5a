infix using "C:\Users\<USER>\Desktop\CPI_CODEBOOK\Sup Data\DATA\allID_1.dct"

label variable outlet_uuid "Outlet_uuid"
label variable r_type   "R_type"
label variable enumeration_market_em "ENUMERATION MARKET (EM)"
label variable lga      "LGA"
label variable district_code "DISTRICT CODE"
label variable district__name "District _Name"
label variable settlement "SETTLEMENT"
label variable introduction "INTRODUCTION"
label variable interview_result "Interview Result"
label variable name_of_outlet "1.5. NAME OF OUTLET"
label variable type_of_outlet "1.16. TYPE OF OUTLET"
label variable phone_number_of_outlet "1.17. PHONE NUMBER OF OUTLET"
label variable nd_phone_number_of_outlet "1.17. 2ND PHONE NUMBER OF OUTLET"
label variable email_address_of_outlet "1.8. EMAIL ADDRESS OF OUTLET"
label variable street_address_of_outlet "1.9 STREET ADDRESS OF OUTLET"
label variable contact_person_name "2.1. CONTACT PERSON NAME"
label variable contact_person_email "2.2. CONTACT PERSON EMAIL"
label variable position "2.3. POSITION"
label variable other_specify_position "2.3. Other Specify(Position)"
label variable phone_number_1 "2.4. Phone Number 1"
label variable phone_number_2 "2.5. Phone Number 2"
label variable interviewer_code "Interviewer code"
label variable interviewer_name "Interviewer Name"
label variable interview_start_date "Interview Start Date"
label variable interview_start_time "Interview Start Time"
label variable interview_end_time "Interview End Time"

#delimit ;
label define R_TYPE  
     1 "Urban"
     2 "Rural"
;
label define ENUMERATION_MARKET_EM
     1 "KWINELLA"
     2 "WILLINGARA BA"
     3 "JARENG"
     4 "BRIKAMA BA"
     5 "SARE BOJO"
     6 "FATOTO"
     7 "SARE NGAI"
     8 "WASSU"
     9 "KAUR"
    10 "FARAFENNI"
    11 "KERR PATEH"
    12 "NDUNGU KEBBEH"
    13 "BARRA"
    14 "BANJUL"
    15 "BAKAU"
    16 "SEREKUNDA"
    17 "LATRIKUNDA"
    18 "LAMIN"
    19 "BRIKAMA"
    20 "GUNJUR"
    21 "SIBANOR"
    22 "KALAGI"
    23 "SOMA"
    24 "BANSANG"
    25 "KEREWAN"
    26 "KUNTAUR"
    27 " Basse Santo-su"
    28 "FASS NJAGA CHOI"
;
label define LGA     
     1 "Banjul"
     2 "Kanifing"
     3 "Brikama"
     4 "Mansakonko"
     5 "Kerewan"
     6 "Kuntaur"
     7 "Janjanbureh"
     8 "Basse"
;
label define DISTRICT_CODE
     1 "KIANG CENTRAL"
     2 "JARRA CENTRAL"
     3 "NIAMINA EAST"
     4 " LOWE FULLADOU WEST"
     5 "JIMARA"
     6 "KANTORA"
     7 "WULI WEST"
     8 "NIANI"
     9 "LOWER SALOUNM"
    10 "UPPER BADIBOU"
    11 "LOWER NUIMI"
    12 "LOWER NUIMI"
    13 "LOWER NUIMI"
    14 "BCC"
    15 "KMC"
    16 "KMC"
    17 "KMC"
    18 "WCR"
    19 "WCR"
    20 "WCR"
    21 "FONI BINTANG"
    22 "FONI JARROL"
    23 "JARRA WEST"
    24 " Upper FULLADOU WEST"
    25 "LOWER BADIBOU"
    26 "NIANI"
    27 "BASSE"
    28 "LOWER NUIMI"
;
label define SETTLEMENT
     1 "KWINELLA"
     2 "WILLINGARA BA"
     3 "JARENG"
     4 "BRIKAMA BA"
     5 "SARE BOJO"
     6 "FATOTO"
     7 "SARE NGAI"
     8 "WASSU"
     9 "KAUR"
    10 "FARAFENNI"
    11 "KERR PATEH"
    12 "NDUNGU KEBBEH"
    13 "BARRA"
    14 "BANJUL"
    15 "BAKAU"
    16 "SEREKUNDA"
    17 "LATRIKUNDA"
    18 "LAMIN"
    19 "BRIKAMA"
    20 "GUNJUR"
    21 "SIBANOR"
    22 "KALAGI"
    23 "SOMA"
    24 "BANSANG"
    25 "KEREWAN"
    26 "KUNTAUR"
    27 "BASSE"
    28 "FASS NJAGA CHOI"
;
label define INTRODUCTION
     1 "Yes"
     2 "No"
;
label define INTERVIEW_RESULT
     1 "Completed"
;
label define TYPE_OF_OUTLET
     1 "Open Market"
     2 "Public Service Enterprise"
     3 "Specialised Shop"
     4 "Neighborhood Shop"
     5 "Mechanic"
     6 "Supermaket"
     7 "Ninimarket"
     8 "Departmental Shop"
     9 "vendor"
;
label define POSITION
     1 "Owner"
     2 "Assistant"
     3 "Other Specify"
;

#delimit cr
label values r_type   R_TYPE  
label values enumeration_market_em ENUMERATION_MARKET_EM
label values lga      LGA     
label values district_code DISTRICT_CODE
label values settlement SETTLEMENT
label values introduction INTRODUCTION
label values interview_result INTERVIEW_RESULT
label values type_of_outlet TYPE_OF_OUTLET
label values position POSITION
