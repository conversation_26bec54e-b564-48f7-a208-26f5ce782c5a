<!DOCTYPE html>
<html lang="en" class="light-style" dir="ltr" data-theme="theme-default" data-assets-path="../assets/" data-template="mobile-template">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=1.0, maximum-scale=3.0" />
    <title>Mobile Dashboard | Supervisor</title>
    <meta name="description" content="Mobile-optimized supervisor dashboard for CPI data collection management" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="./assets/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet" />

    <!-- Icons -->
    <link rel="stylesheet" href="./assets/vendor/fonts/boxicons.css" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="./assets/vendor/css/core.css" class="template-customizer-core-css" />
    <link rel="stylesheet" href="./assets/vendor/css/theme-default.css" class="template-customizer-theme-css" />
    <link rel="stylesheet" href="./assets/css/demo.css" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="./assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />
    <link rel="stylesheet" href="./assets/vendor/libs/apex-charts/apex-charts.css" />

    <!-- Mobile-specific CSS -->
    <style>
        /* Mobile-first responsive design for supervisor */
        .mobile-container {
            padding: 0.5rem;
            max-width: 100%;
            overflow-x: hidden;
        }

        .mobile-card {
            margin-bottom: 1rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .mobile-card:active {
            transform: scale(0.98);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 12px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card.success { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-card.warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .stat-card.danger { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); }
        .stat-card.info { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333; }
        .stat-card.supervisor { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.85rem;
            opacity: 0.9;
        }

        .mobile-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .case-item {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }

        .case-item:last-child {
            border-bottom: none;
        }

        .case-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.5rem;
        }

        .case-id {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .case-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: #333;
        }

        .case-details {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 0.5rem;
        }

        .case-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .case-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .enumerator-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-completed { background: #d4edda; color: #155724; }
        .status-partial { background: #fff3cd; color: #856404; }
        .status-unavailable { background: #f8d7da; color: #721c24; }
        .status-not-found { background: #f8d7da; color: #721c24; }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: background 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .action-btn:hover {
            background: #0056b3;
        }

        .action-btn.danger {
            background: #dc3545;
        }

        .action-btn.danger:hover {
            background: #c82333;
        }

        .sync-btn {
            position: fixed;
            bottom: 80px;
            right: 20px;
            background: #28a745;
            color: white;
            border: none;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            cursor: pointer;
            z-index: 1000;
            transition: transform 0.2s ease;
        }

        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #17a2b8;
            color: white;
            border: none;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            cursor: pointer;
            z-index: 1000;
            transition: transform 0.2s ease;
        }

        .sync-btn:hover, .refresh-btn:hover {
            transform: scale(1.1);
        }

        .header-mobile {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            margin: -0.5rem -0.5rem 1rem -0.5rem;
            text-align: center;
        }

        .header-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .header-subtitle {
            font-size: 0.85rem;
            opacity: 0.9;
        }

        .get-data-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            margin-top: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .get-data-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        /* Chart container for mobile */
        .mobile-chart {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }

        /* Summary cards */
        .summary-section {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .summary-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .summary-label {
            font-size: 0.9rem;
            color: #666;
        }

        .summary-value {
            font-weight: 600;
            color: #333;
        }

        /* Toast notification for mobile */
        .mobile-toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #17a2b8;
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1001;
            display: none;
        }

        /* Loading spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .case-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }

            .case-meta {
                flex-direction: column;
                align-items: flex-start;
            }

            .action-buttons {
                margin-top: 0.5rem;
            }
        }
    </style>

    <!-- Helpers -->
    <script src="./assets/vendor/js/helpers.js"></script>
    <script src="./assets/js/config.js"></script>
</head>

<body>
    <div class="mobile-container">
        <!-- Header -->
        <div class="header-mobile">
            <div class="header-title">Supervisor Dashboard</div>
            <div class="header-subtitle">Report generated at ~~timestring()~~</div>
            <button class="get-data-btn" onclick="CSPro.runLogicAsync('syncWithInterviewer();')">
                <i class="bx bx-download me-1"></i>Get Data
            </button>
        </div>

        <!-- CSPro Data Processing -->
        <?
            numeric i=0;
        ?>
        <?
            numeric Total_count = countcases(CPI_CODEBOOK_DICT);
            numeric partial_count = 0;
            numeric completed_count = 0;
            numeric tot_Refusal = 0;
            numeric not_found = 0;
            numeric others = 0;
            string partialCase, zdate, status;

            forcase CPI_CODEBOOK_DICT do
                if  !ispartial(CPI_CODEBOOK_DICT) and INTRODUCTION = 1 then
                inc(completed_count);
                endif;
            endfor;

            forcase CPI_CODEBOOK_DICT do
                if ispartial(CPI_CODEBOOK_DICT) then
                    inc(partial_count);
                    elseif INTERVIEW_RESULT = 3 then
                    inc(tot_Refusal);
                    elseif INTERVIEW_RESULT = 4 then
                    inc(not_found);
                    elseif INTERVIEW_RESULT = 98 then
                    inc(others);
                endif;
            endfor;
        ?>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card info">
                <div class="stat-number">~~Total_count~~</div>
                <div class="stat-label">Total Cases</div>
            </div>
            <div class="stat-card success">
                <div class="stat-number">~~completed_count~~</div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-number">~~partial_count~~</div>
                <div class="stat-label">Partial</div>
            </div>
            <div class="stat-card danger">
                <div class="stat-number">~~tot_Refusal~~</div>
                <div class="stat-label">Unavailable</div>
            </div>
            <div class="stat-card danger">
                <div class="stat-number">~~not_found~~</div>
                <div class="stat-label">Not Found</div>
            </div>
            <div class="stat-card supervisor">
                <div class="stat-number">~~others~~</div>
                <div class="stat-label">Others</div>
            </div>
        </div>

        <!-- Summary Section -->
        <div class="summary-section">
            <div class="summary-title">
                <i class="bx bx-bar-chart-alt-2"></i>
                Other Statistics
            </div>
            <div class="summary-item">
                <span class="summary-label">Not Found</span>
                <span class="summary-value">~~not_found~~</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Partially Completed</span>
                <span class="summary-value">~~partial_count~~</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Other Reasons</span>
                <span class="summary-value">~~others~~</span>
            </div>
        </div>

        <!-- Performance Chart -->
        <div class="mobile-chart">
            <div class="chart-title">Performance Overview</div>
            <div id="supervisorPerformanceChart" style="height: 200px;"></div>
            <div style="text-align: center; margin-top: 1rem;">
                <small class="text-muted">Performance is measured against sync time from the server</small>
                <br>
                <small class="text-muted">This module is still under development</small>
            </div>
        </div>

        <!-- Case Report Table -->
        <div class="mobile-table">
            <div class="table-header">
                <span><i class="bx bx-list-ul me-2"></i>Case Reports</span>
                <span style="font-size: 0.8rem; opacity: 0.9;">~~Total_count~~ total</span>
            </div>

            <!-- Partial Cases -->
            <? forcase CPI_CODEBOOK_DICT do
                inc(i);
                if  ispartial(CPI_CODEBOOK_DICT) then
                    partialCase = key(CPI_CODEBOOK_DICT);
                    status = "Partial";
                    string enum = S_NAME;
            ?>
            <div class="case-item">
                <div class="case-header">
                    <div class="case-id">ID: ~~Maketext(partialCase)~~</div>
                </div>
                <div class="case-name">~~strip(NAME_OF_OUTLET)~~</div>
                <div class="case-details">
                    EM: ~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~ |
                    Date: ~~strip(INTERVIEW_START_DATE)~~
                </div>
                <div class="case-meta">
                    <div>
                        <span class="case-status status-partial">~~strip(status)~~</span>
                        <span class="enumerator-badge">~~strip(enum)~~</span>
                    </div>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="CSPro.runLogicAsync('modify_case(~~i~~);')" title="Edit">
                            <i class="bx bx-edit-alt"></i>Edit
                        </button>
                        <button class="action-btn danger" onclick="javascript:void(0);" title="Discard">
                            <i class="bx bx-trash"></i>Discard
                        </button>
                    </div>
                </div>
            </div>
            <? endif;
            endfor; ?>

            <!-- Unavailable Cases -->
            <? forcase CPI_CODEBOOK_DICT do
                if  !ispartial(CPI_CODEBOOK_DICT) and INTERVIEW_RESULT = 3 then
                    partialCase = key(CPI_CODEBOOK_DICT);
                    status = "Unavailable";
                    string enum = INTERVIEWER_NAME;
            ?>
            <div class="case-item">
                <div class="case-header">
                    <div class="case-id">ID: ~~Maketext("%s",partialCase)~~</div>
                </div>
                <div class="case-name">~~strip(NAME_OF_OUTLET)~~</div>
                <div class="case-details">
                    EM: ~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~ |
                    Date: ~~strip(INTERVIEW_START_DATE)~~
                </div>
                <div class="case-meta">
                    <div>
                        <span class="case-status status-unavailable">~~strip(status)~~</span>
                        <span class="enumerator-badge">~~strip(enum)~~</span>
                    </div>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="CSPro.runLogicAsync('modify_case(~~i~~);')" title="Edit">
                            <i class="bx bx-edit-alt"></i>Edit
                        </button>
                        <button class="action-btn danger" onclick="javascript:void(0);" title="Discard">
                            <i class="bx bx-trash"></i>Discard
                        </button>
                    </div>
                </div>
            </div>
            <? endif;
            endfor; ?>

            <!-- Not Found Cases -->
            <? forcase CPI_CODEBOOK_DICT do
                if  INTERVIEW_RESULT = 4 then
                    partialCase = key(CPI_CODEBOOK_DICT);
                    status = "Not Found";
                    string enum = INTERVIEWER_NAME;
            ?>
            <div class="case-item">
                <div class="case-header">
                    <div class="case-id">ID: ~~Maketext("%s",partialCase)~~</div>
                </div>
                <div class="case-name">~~strip(NAME_OF_OUTLET)~~</div>
                <div class="case-details">
                    EM: ~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~ |
                    Date: ~~strip(INTERVIEW_START_DATE)~~
                </div>
                <div class="case-meta">
                    <div>
                        <span class="case-status status-not-found">~~strip(status)~~</span>
                        <span class="enumerator-badge">~~strip(enum)~~</span>
                    </div>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="CSPro.runLogicAsync('modify_case(~~i~~);')" title="Edit">
                            <i class="bx bx-edit-alt"></i>Edit
                        </button>
                        <button class="action-btn danger" onclick="javascript:void(0);" title="Discard">
                            <i class="bx bx-trash"></i>Discard
                        </button>
                    </div>
                </div>
            </div>
            <? endif;
            endfor; ?>
        </div>

        <!-- Sync Button -->
        <button class="sync-btn" onclick="CSPro.runLogicAsync('syncWithInterviewer();')" title="Sync Data">
            <i class="bx bx-sync"></i>
        </button>

        <!-- Refresh Button -->
        <button class="refresh-btn" onclick="location.reload()" title="Refresh Dashboard">
            <i class="bx bx-refresh"></i>
        </button>

        <!-- Toast Notification -->
        <div id="supervisorToast" class="mobile-toast">
            <strong>Data Sync!</strong> No data has been sent to the server.
        </div>
    </div>

    <!-- Core JS -->
    <script src="./assets/vendor/libs/jquery/jquery.js"></script>
    <script src="./assets/vendor/libs/popper/popper.js"></script>
    <script src="./assets/vendor/js/bootstrap.js"></script>
    <script src="./assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js"></script>
    <script src="./assets/vendor/js/menu.js"></script>

    <!-- Vendors JS -->
    <script src="./assets/vendor/libs/apex-charts/apexcharts.js"></script>

    <!-- Main JS -->
    <script src="./assets/js/main.js"></script>

    <!-- Mobile-specific JavaScript -->
    <script>
        // Mobile-optimized supervisor dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize supervisor performance chart
            initSupervisorPerformanceChart();

            // Add touch feedback for interactive elements
            addTouchFeedback();

            // Show sync notification if needed
            showSyncNotification();
        });

        function initSupervisorPerformanceChart() {
            const chartElement = document.querySelector('#supervisorPerformanceChart');
            if (!chartElement) return;

            // Sample data for supervisor overview - replace with actual CSPro data
            const options = {
                series: [
                    {
                        name: 'Completed',
                        data: [~~completed_count~~, 0, 0, 0, 0, 0, 0]
                    },
                    {
                        name: 'Partial',
                        data: [0, ~~partial_count~~, 0, 0, 0, 0, 0]
                    },
                    {
                        name: 'Unavailable',
                        data: [0, 0, ~~tot_Refusal~~, 0, 0, 0, 0]
                    }
                ],
                chart: {
                    type: 'bar',
                    height: 200,
                    toolbar: { show: false },
                    stacked: true
                },
                colors: ['#4facfe', '#fa709a', '#ff6b6b'],
                plotOptions: {
                    bar: {
                        horizontal: false,
                        columnWidth: '60%',
                        borderRadius: 4
                    }
                },
                xaxis: {
                    categories: ['Status Overview'],
                    labels: {
                        style: { fontSize: '12px' }
                    }
                },
                yaxis: {
                    labels: {
                        style: { fontSize: '12px' }
                    }
                },
                legend: {
                    position: 'bottom',
                    fontSize: '12px'
                },
                grid: {
                    borderColor: '#f0f0f0',
                    strokeDashArray: 3
                },
                dataLabels: {
                    enabled: true,
                    style: {
                        fontSize: '11px',
                        colors: ['#fff']
                    }
                },
                tooltip: {
                    theme: 'light',
                    style: { fontSize: '12px' }
                }
            };

            const chart = new ApexCharts(chartElement, options);
            chart.render();
        }

        function addTouchFeedback() {
            // Add haptic feedback for touch devices
            const interactiveElements = document.querySelectorAll('.action-btn, .sync-btn, .refresh-btn, .stat-card, .get-data-btn');

            interactiveElements.forEach(element => {
                element.addEventListener('touchstart', function() {
                    if (navigator.vibrate) {
                        navigator.vibrate(50); // Short vibration
                    }
                });
            });
        }

        function showSyncNotification() {
            // Show sync notification if there are cases to sync
            const totalCases = ~~Total_count~~;
            if (totalCases === 0) {
                const toast = document.getElementById('supervisorToast');
                toast.style.display = 'block';
                setTimeout(() => {
                    toast.style.display = 'none';
                }, 5000);
            }
        }

        // Enhanced sync function with loading state
        function syncWithInterviewer() {
            const syncBtn = document.querySelector('.sync-btn');
            const originalContent = syncBtn.innerHTML;

            // Show loading state
            syncBtn.innerHTML = '<div class="loading-spinner"></div>';
            syncBtn.disabled = true;

            // Call CSPro sync function
            CSPro.runLogicAsync('syncWithInterviewer();').then(() => {
                // Reset button after sync
                setTimeout(() => {
                    syncBtn.innerHTML = originalContent;
                    syncBtn.disabled = false;
                    location.reload(); // Refresh to show updated data
                }, 2000);
            }).catch(() => {
                // Handle error
                syncBtn.innerHTML = originalContent;
                syncBtn.disabled = false;
            });
        }

        // Optimize for mobile performance
        function optimizeForMobile() {
            // Lazy load images
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));

            // Optimize scroll performance
            let ticking = false;
            function updateScrollPosition() {
                // Add scroll-based optimizations here
                ticking = false;
            }

            function requestTick() {
                if (!ticking) {
                    requestAnimationFrame(updateScrollPosition);
                    ticking = true;
                }
            }

            window.addEventListener('scroll', requestTick);
        }

        // Initialize mobile optimizations
        optimizeForMobile();

        // Add pull-to-refresh functionality
        let startY = 0;
        let pullDistance = 0;
        const pullThreshold = 100;

        document.addEventListener('touchstart', function(e) {
            startY = e.touches[0].pageY;
        });

        document.addEventListener('touchmove', function(e) {
            if (window.scrollY === 0) {
                pullDistance = e.touches[0].pageY - startY;
                if (pullDistance > 0 && pullDistance < pullThreshold) {
                    // Visual feedback for pull-to-refresh
                    document.body.style.transform = `translateY(${pullDistance * 0.5}px)`;
                }
            }
        });

        document.addEventListener('touchend', function() {
            if (pullDistance > pullThreshold && window.scrollY === 0) {
                location.reload();
            }
            document.body.style.transform = '';
            pullDistance = 0;
        });
    </script>
</body>
</html>
