"""
Application settings management
"""
import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

from src.core.database.connection import ConnectionProfile

logger = logging.getLogger(__name__)

class Settings:
    """Application settings manager"""
    # Class variables for static access
    _APP_NAME = "csdb-converter"
    _CONFIG_DIR = Path.home() / f".{_APP_NAME}"

    @classmethod
    def get_app_data_dir(cls) -> str:
        """Get application data directory"""
        app_data_dir = cls._CONFIG_DIR / "data"
        os.makedirs(app_data_dir, exist_ok=True)
        return str(app_data_dir)

    def __init__(self):
        self._config_dir = self._CONFIG_DIR
        self._config_file = self._config_dir / "settings.json"
        self._settings = {
            "recent_files": [],
            "connection_profiles": [],
            "last_profile": "",
            "batch_size": 1000,
            "default_encoding": "utf-8",
            "table_prefix": "",
            "max_concurrent_conversions": 3
        }

        # Create config directory if it doesn't exist
        self._config_dir.mkdir(parents=True, exist_ok=True)

        # Load settings
        self.load()

    def load(self) -> None:
        """Load settings from file"""
        if not self._config_file.exists():
            logger.info("Settings file not found, using defaults")
            return

        try:
            with open(self._config_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                self._settings.update(data)
            logger.info("Settings loaded successfully")
        except Exception as e:
            logger.error(f"Error loading settings: {e}")

    def save(self) -> None:
        """Save settings to file"""
        try:
            with open(self._config_file, "w", encoding="utf-8") as f:
                json.dump(self._settings, f, indent=2)
            logger.info("Settings saved successfully")
        except Exception as e:
            logger.error(f"Error saving settings: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """Get setting value"""
        return self._settings.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """Set setting value"""
        self._settings[key] = value
        self.save()

    def add_recent_file(self, file_path: str) -> None:
        """Add file to recent files list"""
        recent_files = self.get("recent_files", [])

        # Remove if already exists
        if file_path in recent_files:
            recent_files.remove(file_path)

        # Add to beginning of list
        recent_files.insert(0, file_path)

        # Limit to 10 recent files
        recent_files = recent_files[:10]

        self.set("recent_files", recent_files)

    def get_recent_files(self) -> List[str]:
        """Get list of recent files"""
        return self.get("recent_files", [])

    def clear_recent_files(self) -> None:
        """Clear recent files list"""
        self.set("recent_files", [])

    def save_connection_profiles(self, profiles: List[ConnectionProfile]) -> None:
        """Save connection profiles"""
        profile_data = [p.to_dict() for p in profiles]
        self.set("connection_profiles", profile_data)

    def load_connection_profiles(self) -> List[Dict]:
        """Load connection profiles data"""
        return self.get("connection_profiles", [])

    def set_last_profile(self, profile_name: str) -> None:
        """Set last used profile"""
        self.set("last_profile", profile_name)

    def get_last_profile(self) -> str:
        """Get last used profile"""
        return self.get("last_profile", "")

    def get_batch_size(self) -> int:
        """Get batch size for data processing"""
        return self.get("batch_size", 1000)

    def set_batch_size(self, size: int) -> None:
        """Set batch size for data processing"""
        self.set("batch_size", size)

    def get_default_encoding(self) -> str:
        """Get default character encoding"""
        return self.get("default_encoding", "utf-8")

    def set_default_encoding(self, encoding: str) -> None:
        """Set default character encoding"""
        self.set("default_encoding", encoding)

    def get_table_prefix(self) -> str:
        """Get table name prefix"""
        return self.get("table_prefix", "")

    def set_table_prefix(self, prefix: str) -> None:
        """Set table name prefix"""
        self.set("table_prefix", prefix)

    def get_max_concurrent_conversions(self) -> int:
        """Get maximum number of concurrent conversions"""
        return self.get("max_concurrent_conversions", 3)

    def set_max_concurrent_conversions(self, count: int) -> None:
        """Set maximum number of concurrent conversions"""
        self.set("max_concurrent_conversions", count)
