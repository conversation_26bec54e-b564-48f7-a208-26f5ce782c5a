infix using "C:\Users\<USER>\Desktop\CPI_CODEBOOK\Sup Data\DATA\outletID.dct"

label variable outlet_uuid "Outlet_uuid"
label variable r_type   "R_type"
label variable enumeration_market_em "ENUMERATION MARKET (EM)"
label variable item_id  "Item_ID"
label variable name_of_item "1.7 NAME OF ITEM"
label variable discription_of_item "1.8  DISCRIPTION OF ITEM"
label variable relevance "1.9 Relevance"
label variable imp_local "1.10. What is the origine of the item"
label variable coicop06_item_code "1.11. COICOP06( item code)"
label variable uom      "1.12. What is the unit of meaurment (UoM)"
label variable qty202101 "1.13.  What is the quantity"
label variable price202101 "1.14. What is the price"
label variable rcheck   "rCheck"

#delimit ;
label define R_TYPE  
     1 "Urban"
     2 "Rural"
;
label define ENUMERATION_MARKET_EM
     1 "KWINELLA"
     2 "WILLINGARA BA"
     3 "JARENG"
     4 "BRIKAMA BA"
     5 "SARE BOJO"
     6 "FATOTO"
     7 "SARE NGAI"
     8 "WASSU"
     9 "KAUR"
    10 "FARAFENNI"
    11 "KERR PATEH"
    12 "NDUNGU KEBBEH"
    13 "BARRA"
    14 "BANJUL"
    15 "BAKAU"
    16 "SEREKUNDA"
    17 "LATRIKUNDA"
    18 "LAMIN"
    19 "BRIKAMA"
    20 "GUNJUR"
    21 "SIBANOR"
    22 "KALAGI"
    23 "SOMA"
    24 "BANSANG"
    25 "KEREWAN"
    26 "KUNTAUR"
    27 " Basse Santo-su"
    28 "FASS NJAGA CHOI"
;
label define NAME_OF_ITEM
     1 "Long-Grained Rice (Imported)"
     2 "Paddy Rice Long Grain (Local)"
     3 "Medium-Grained Rice (Imported)"
     4 "Small grained rice (imported)"
     5 "Basmati Rice (Imported)"
     6 "Maize"
     7 "Millet"
     8 "Bread"
     9 "Findi"
    10 "Millet Flour"
    11 "Spaghetti"
    12 "Fish Pie"
    13 "Cake (pan, etc.)"
    14 "Gari (Cassava Flour)"
    15 "Beef"
    16 "Sheep meat (mutton)"
    17 "Chicken Legs (Imported)"
    18 "Chicken (Local)"
    19 "Pork"
    20 "Canned meat (200 Gram)"
    21 "Goat Meat"
    22 "Fresh Bonga"
    23 "Smoked Bonga"
    24 "Cat Fish"
    25 "Fresh Grouper/Lady Fish"
    26 "Fresh Barracuda"
    27 "Dried Couta/Tenny"
    28 "Shrimps"
    29 "Tilapia"
    30 "Sardine (Tin Fish 125g)"
    31 "Dried fish"
    32 "Eggs"
    33 "Fresh Milk"
    34 "Sour Milk"
    35 "Evaporated Milk (Peak)"
    36 "Powdered Milk(400g)"
    37 "Milk Sacket 25g (e.g. Vitalait,sophie,peak)"
    38 "Yoghurt(125g)"
    39 "Palm Oil"
    40 "Butter (250Gram)"
    41 "Vegetable Oil (Sold loose)"
    42 "Peanut Buter"
    43 "Margarine (500 Grams)"
    44 "Groundnuts-Shelled"
    45 "Raw Groundnut Powder"
    46 "Banana"
    47 "Oranges"
    48 "Mangoes"
    49 "Lime"
    50 "Apple"
    51 "Baobab fruit"
    52 "Paw - Paw"
    53 "Water Melon"
    54 "Roasted Groundnut"
    55 "Dates"
    56 "Potatoes (Irish)"
    57 "Sweet Potatoes"
    58 "Cassava"
    59 "Dry Beans"
    60 "Small Pepper-Fresh"
    61 "Tomatoes-Fresh"
    62 "Bitter Tomato"
    63 "Garden Eggs"
    64 "Okra"
    65 "Onion"
    66 "Pumpkin"
    67 "Big Red Pepper"
    68 "Kren-Kren"
    69 "Bisap"
    70 "Cabbage"
    71 "Lettuce (Salad)"
    72 "Tomato Puree (Paste,70g)"
    73 "Carrot"
    74 "Cucumber"
    75 "Cassava Leaves"
    76 "Sugar"
    77 "Mint Fresh"
    78 "Chewing gum (Hollywood)"
    79 "Honey"
    80 "Ice Cream (Bowl 1 Litre)"
    81 "Mint Stick"
    82 "Biscuit (E.g.Bidew)"
    83 "Salt"
    84 "Garlic"
    85 "Maggi Cube"
    86 "Small Dry Pepper"
    87 "Locust Beans (Neteetu)"
    88 "Black Pepper (Whole Seed)"
    89 "Vinegar"
    90 "Chilli powder"
    91 "Mayonnaise"
    92 "Tea Bags(200g)"
    93 "Coffee-Nescafe (50gr)"
    94 "Powdered Tea (Ovaltine, 200gr)"
    95 "Juices (Wonjo)"
    96 "Chinese Green Tea (25g) (Ataya)"
    97 "Soft Drinks (Coke, Spirite, Fanta)"
    98 "ineral Water (Malta, Cocktail,Vimto, 330m"
    99 "Cold water (1.5Ltr)"
   100 "Spirits"
   101 "Wines (Red, White, etc.)"
   102 "Palm Wine"
   103 "Beer (lager and porter)"
   104 "Stout(330ml)"
   105 "Piccadilly"
   106 "Marlboro"
   107 "Benson"
   108 "anise (Tobacco) Wrapped in Paper 50Gra"
   109 "Bond"
   110 "Snuff"
   111 "Rizzla"
   112 "Kola Nuts"
   113 "Bitter Cola Nut"
   114 "Material for trouser"
   115 "Babies' Clothes (Napkin)"
   116 "Boy's Underpants (4-6 Years)"
   117 "Boy's Shirt (12-16 Years)"
   118 "Boy's Trousers (12-16 Years)"
   119 "Ladies Docket"
   120 "Ladies Dress"
   121 "Ladies Underwear"
   122 "Lady's Blouse"
   123 "Men's Shirts"
   124 "Men's Trousers"
   125 "Men's Underwear"
   126 "Singlet"
   127 "Track Suits"
   128 "T-Shirt"
   129 "Girl's Dress (10 Years)"
   130 "Brassiere"
   131 "Men's Sock"
   132 "School Uniform (Grade 1)"
   133 "Laundry (Hand Wash, Trousers and Shirt for Men)"
   134 "Boy's Rubber Sandals (12-16 Years)"
   135 "Boy's Shoes-Plastic (Nyambalastic (12-16 Years)"
   136 "Boy's Slippers (12-16 Years)"
   137 "Boy's Sports Shoes (12-16 Years)"
   138 "Girls's Slippers (Charack, 12-16 Years)"
   139 "Girl's Rubber Sandals (12-16 Years)"
   140 "Girls' Full Shoes-Synthetic (12-16 Years)"
   141 "Men's Full Shoe"
   142 "Men's Slipper"
   143 "Men's Sports Shoes"
   144 "Women's Full Shoes"
   145 "Women's Slippers"
   146 "One Bedroom Sitter (Single Room)"
   147 "One Bedroom (Room & Parlour)"
   148 "Two Bedroom Without Toilet & Kitchen Facility"
   149 "Facility"
   150 "Three Bedroom Without Facility"
   151 "Three Bedroom With Facility"
   152 "Cement"
   153 "Ceramic Tiles (Indian)"
   154 "Joints (Three-Quarter T-Joint)"
   155 "Oil Paints (3.6)"
   156 "Water Paints (18 Ltr)"
   157 "Electric Pipes (20 mm, White)"
   158 "Plastering of Room (3 by 3 meter room size)"
   159 "Tilling of Room (3 by 3 meter room size)"
   160 "Painting of Room (3 by 3 meter room size)"
   161 "Window Frame Set (Aluminium, Standard Window)"
   162 "Water Charges (10 cum)"
   163 "Electricity (300 kwh)"
   164 "Gas"
   165 "Charcoal"
   166 "Firewood"
   167 "Wooden Bed (Box Bed With Decorated Head)"
   168 "Bedstead"
   169 "Sofa Chair (One Set)"
   170 "Mattress (Double Bed 8 Inches)"
   171 "Vynil Floor Cover (Thick, 1 Mtr)"
   172 "Ready-made Curtains (1 Packet)"
   173 "Mosquito Net"
   174 "Mat (sleeping/Praying)"
   175 "Bed Sheet (Double Bed)"
   176 "Pedestal Fan"
   177 "Freezer (Upright)"
   178 "Generator"
   179 "Iron (charcoal)"
   180 "Solar panel (With Inverter)"
   181 "Refrigerator With Freezer (165-190 Ltr)"
   182 "Cooking Knife"
   183 "Frying Pan"
   184 "Negro Pot"
   185 "Thermos Flask"
   186 "Bucket (Plastic)"
   187 "Plastic Basin"
   188 "Plastic Pan"
   189 "Batteries"
   190 "Torch Light"
   191 "Wheel Barrow"
   192 "Bleach (Ordsavel)"
   193 "Candle"
   194 "Insecticides Sprays (400 mle)"
   195 "Lighters"
   196 "Matches"
   197 "Soda Powder"
   198 "Soda Soap"
   199 "Starch (Dakandeh)"
   200 "Incense (Stick, 1 Packet)"
   201 "Light Bulb (Energy Bulb)"
   202 "Maid servants-Full Time"
   203 "Drivers"
   204 "Amoxilliyin"
   205 "Aspirin"
   206 "Coartem (Adult 1 Doze)"
   207 "Ibrufen (10 Tablets)"
   208 "Paracetamol (1 strip of 10 tablets)"
   209 "Out-Patient Fees (Ticket Paid)"
   210 "Doctor's Fee (Public Health Facility)"
   211 "Doctor's Fee (Private Clinic)"
   212 "Dental Costs (Tooth Uproot)"
   213 "Hospital Fees (Hospitalization, Public)"
   214 "Car (Toyota Yaris)"
   215 "Motorcycle (fellenco/safari, 125cc)"
   216 "Purchase of Bicycle"
   217 "Bicycle Tyre"
   218 "Motor vehicle Tyre Tube (Inner)"
   219 "Puncture Repair"
   220 "Servicing of Motor Vehicle (Brake Repair, Labour)"
   221 "Bicycle Tyre"
   222 "Car Battery"
   223 "Diesel"
   224 "Motor Oil (4.5 Ltr)"
   225 "Petrol"
   226 "Bus Fares (Seven Kilometers)"
   227 "Taxi Fares (Shared Taxi, Fixed Route)"
   228 "School Bus Fare (Single Ticket)"
   229 "International Flights (Return Ticket, Six Hours)"
   230 "Ferry Fares (Big Ferry, Single Ticket)"
   231 "Letter Postage (International)"
   232 "Letter Postage (Local)"
   233 "Fixed line Telephone Set (one Piece)"
   234 "Mobile phone (Itel)"
   235 "Internet Costs (1 Hrs, Cyber Café)"
   236 "E-Credit"
   237 "Memory Card Chip (4 GB)"
   238 "Television Set (LG, LED, 32, SAMSUMG)"
   239 "Radio (12 MB)"
   240 "Radio Cassette Player (4 Batteries)"
   241 "Ticket for Football Game (National League)"
   242 "Videos Club Ticket (Football Match)"
   243 "Weekend Dance Charges (Saturday Night)"
   244 "Text Books(Grade 7)"
   245 "Newspapers"
   246 "Magazines"
   247 "Pen"
   248 "Envelopes"
   249 "School Fees (Private School, Grade 7)"
   250 "Examination Fees (Private School, Grade 9)"
   251 "Rice With Side Dishe (1 plate, Domoda,Yassa, etc.)"
   252 "Tea with Milk (1 Cup of Tea)"
   253 "Hotel (1 Night, Three-Star)"
   254 "Lodge (1 Night, Without Air Conditioner)"
   255 "Beautician (Facial Makeup, 1 Service)"
   256 "Barber (Hair Cut, 1 Service)"
   257 "Beauty Salon (Hair Straightening, 1 Service)"
   258 "Face Powder (Makeup Powder)"
   259 "Nail Polish"
   260 "Razor Blade"
   261 "Shampoo (1000 ml)"
   262 "Skin Light (500 ml)"
   263 "Toilet Soap (200g)"
   264 "Tooth Brush (Adult)"
   265 "Tooth Paste (Colgate 50ml)"
   266 "Necklace (Gold Imitation, Simple)"
   267 "Two Bedroom Without Toilet & Kitchen Facility"
   268 "Motor vehicle Tyre Tube (Inner)"
;
label define DISCRIPTION_OF_ITEM
     1 "nown brand,white rice(milled) ,sold"
     2 "Paddy rice long grain (Local)"
     3 "Medium-Grained Rice (Imported)"
     4 "brand,white rice(milled) ,sold loose;n"
     5 "Basmati Rice (imported)"
     6 "Whole grain(use 1kg tomato cup)"
     7 "Whole grain(use 1kg tomato cup)"
     8 "Long brown from bakery (Tapalapa)"
     9 "Whole grain(use big tomato cup)"
    10 "white in powdered form"
    11 "ss maximum quantity 250grams, pre"
    12 "of fish (sold loose) 150grams to 200"
    13 "sold loose, 150 grams to 200grams"
    14 "wdered cassava,white in colour(0.5"
    15 "meat and bone, cow/bull"
    16 "meat and bone, sheep"
    17 "whole legs frozen"
    18 "medium size, live local breed"
    19 "meat and bone, pig"
    20 "chicken luncheon,200g"
    21 "meat and bone"
    22 "fresh medium size fish"
    23 "freshly smoked, medium size fish"
    24 "freshly smoked, medium size"
    25 "medium size fish"
    26 "medium size fish"
    27 "medium size fish"
    28 "Small edible crustasean"
    29 "Meduim size Fresh fish"
    30 "Tin Fish 125g,fish in vegaetable oil"
    31 "100-250 grams sold loose"
    32 "fresh egg,imported or local"
    33 "cow milk, unpreserved Fresh Milk"
    34 "Fermented cow milk"
    35 "170 gm, unsweetened (peak milk)"
    36 "125 grams brandless"
    37 "acket 25g (e.g. Vitalait,sophie,cowbe"
    38 "125g,cow milk,fat content 2.5%"
    39 "fresh, without sediments"
    40 "250 gm Salted Butter"
    41 "any brand no sediments"
    42 "Groundnut paste in plastic bundle"
    43 "ic container,(e.g. Rosam, Romi or Re"
    44 "Decorticated nut"
    45 "Sold loose"
    46 "Long Fingers green/yellowish"
    47 "medium size"
    48 "iped fruit,common varieties i.e.(jurr"
    49 "3 to 4 wholes in a heap"
    50 "Golden (green/yellowish)"
    51 "uit that covers the seed; (use 2 kg T"
    52 "A whole paw - paw fruit"
    53 "green one whole fruit"
    54 "Plastic Bag"
    55 "Dried fruit,sold loose"
    56 "clean, intact skin brownish,imported"
    57 "Brown, locally grown vegetable tube"
    58 "ownish vegetable tuber locally grow"
    59 "Common varieties,white beans"
    60 "Red finger shaped, unbroken"
    61 "fresh, medium size vegetable"
    62 "edium size, fresh vegetable,sold loo"
    63 "edium size, fresh vegetable,sold loo"
    64 "m size greenish, fresh vegetable,sold"
    65 "Round onion fresh, big, intact skin"
    66 "fresh, local yellowish in colour"
    67 "big red pepper,Redish in colour"
    68 "fresh, greenish in colour"
    69 "fresh, greenish in colour"
    70 "fresh, green"
    71 "fresh leaves"
    72 "70g,wa-gust'gildaorlitina"
    73 "vegetable ,red roots(Garden product"
    74 "Greenish vegetable fruit"
    75 "sold loose"
    76 "ined, powdered crystallised , sold lo"
    77 "whole, cough candy"
    78 "Hollywood"
    79 "Pure honey, Local Honey"
    80 "In plastic container"
    81 "mintstick (plastic handle)"
    82 "biscuits plain brand (E.g.Bidew)"
    83 "alt, Locally made (Small Cup i.e. 170g"
    84 "Whole"
    85 "jumbo,one cube of 10g"
    86 "ll dried pepper,sold loose in platic b"
    87 "properly boiled"
    88 "whole black seed"
    89 "In plastic bottle container"
    90 "Powdered pepper,sold loose"
    91 "rmanti platic jar,500g plastic/bottle j"
    92 "e-pack 100 bags,sun-island, jolly, olin"
    93 "Instant coffee,Nescafe classic 50 grm"
    94 "owdered tea in tin, commom variet"
    95 "Sweetened local fruit drink"
    96 "een tea in a pack,25g,common varie"
    97 "omestic bottle drink i.e. Fanta 250m"
    98 "300 ml,coctail/malta"
    99 "1.5Ltr in plastic bottle"
   100 "nown brands, i.e. Vodka,whisky and"
   101 "well known brand ,ordinary red win"
   102 "fermented, from palm tree"
   103 "Beer domestic brand,Type:larger"
   104 "inness, (without deposit) Bottle 330"
   105 "20 sticks"
   106 "20 sticks"
   107 "20 sticks"
   108 "Tobacco Powder for smooking"
   109 "20 sticks"
   110 "owdered Tobacco variety ,measure"
   111 "arette papers use for wrapping man"
   112 "Sold loose,medium size"
   113 "Sold loose,medium size"
   114 "polyester 65 % viscose 35%"
   115 "polyester 100%"
   116 "100% Cotton Without brief (4-6 Years)"
   117 "polyester 100% (12-16 Years)"
   118 "ready made trousers (12-16 Years)"
   119 "simple dress polyester 65%"
   120 "simple ladies dress 65% polyester"
   121 "Medium elastrometric fibre, free size"
   122 "100% viscose long sleeved"
   123 "cotton 100%,classic shirt,short sleeve"
   124 "60-65% cotton/ polyester"
   125 "For adult cotton 65%"
   126 "cotton 100% free size"
   127 "cotton 100% polyester long sleeve"
   128 "plain round neck, cotton 100%"
   129 "cotton 100% ,sleevelees (10 Years)"
   130 "80 -90 % synthetic"
   131 "70-100 % cotton,Ankle size socks"
   132 "Tailoring charges for a Pair (Grade 1)"
   133 "Trousers and shirt"
   134 "Rubber sandal shoe for boys (12-16 Years)"
   135 "Plastic shoe for boys (12-16 Years)"
   136 "Plastic for boys (12-16 Years)"
   137 "Immitation with laces,Brandless (12-16 Years)"
   138 " (12-16 Years)"
   139 "Rubber sandal shoe for (12-16 Years)"
   140 "Sole:Synthetic,Classic Shoe (12-16 Years)"
   141 "Sole & heel:Synthetic,Classic Shoe"
   142 "plastic shoe"
   143 "Immitation with laces (e.g. Adidas,Nike)"
   144 "Sole:Synthetic,Classic Shoe"
   145 "plastics shoe"
   146 "House built with cement (Single Room)"
   147 "House built with cement (Room & Parlour)"
   148 "House built with cement"
   149 "House built with cement"
   150 "House built with cement"
   151 "House built with cement"
   152 "Domestics cement"
   153 "Floor ceremic Tile 45 by 45 cm in Size"
   154 "Plumbering T. joint medium"
   155 "3.6 litres"
   156 "18-20 litres"
   157 "20mm ,white,LB"
   158 "3.5 by 3.5 Meter room Size"
   159 "3.5 by 3.5 Meter room Size"
   160 "3.5 by 3.5 Meter room Size"
   161 "1 by 1 meter window"
   162 "10 cubic meter Household consumption"
   163 "Electricity unit consumption charges"
   164 "3 kilogram refiling"
   165 "Use two kilogram tomato cup"
   166 "4-5 sticks,Medium Size"
   167 "Box bed with decorated head"
   168 "Simple metal bed 'cham gerejeff'"
   169 "Sofa for sitting room"
   170 "Double bed 8 inches"
   171 "Floor covering (Thick, 1 Meter)"
   172 "Curtains prepacked"
   173 "Round mosquito Net"
   174 "Mat for praying or sleeping (1 by 2 meter)"
   175 "Bed sheet for a Double Bed"
   176 "Height: 120-140cm,medium size,speed:3-5 (Plastic)"
   177 "Chest defrosting,capacity:160L,LWH:60-60-120"
   178 "Tiger generator"
   179 "Locally made iron"
   180 "Single panel with inverters"
   181 "Refrigerator with freezing compartments"
   182 "Kitchen Knife (Stainless steel)"
   183 "Medium (Stainless Steel)"
   184 "Aluminium (7 kg)"
   185 "3.5 liter contaner,plastic"
   186 "20 liter plastic bucket with cover"
   187 "Plastic basin,Thick Plastic Basin"
   188 "Plastic pan,with two handles"
   189 "Batteries (e.g. KK, etc.)"
   190 "Tiger Head"
   191 "Stainless steel container (tubeless)"
   192 "Liquid Bleach"
   193 "Long and white in colour"
   194 "Bop Spray 400ml"
   195 "Rifiling type"
   196 "Safety match box"
   197 "Caustic Soda"
   198 "Locally made soap with caustic soda"
   199 "Natural Starch (i.e.'dakandeh')"
   200 "Stick Type:Long stick"
   201 "Energy Saving Light bulb (20 Watts)"
   202 "maid servant full time"
   203 "Service of a driver (Private)"
   204 "250mg"
   205 "75mg"
   206 "Anti-Malaria drug,1 doze"
   207 "400mg"
   208 "1 Strip of 10 Tablets"
   209 "Ticket fee"
   210 "Consultation"
   211 "Consultation"
   212 "Cost of Tooth uproot"
   213 "1 week hospitalization (Public)"
   214 "price for a new car (e.g. Toyota Yaris)"
   215 "Fellenco/Safarri 125cc"
   216 "Mountain bike with Gears"
   217 "Inner tyre (18 by 28 R)"
   218 "one new Inner Tube"
   219 "Repair of puctured tyre of a car"
   220 "Labour charges on break Repair"
   221 "Outer tyre (18 by 28 R)"
   222 "Bosch,capacity 12v 55-60AH"
   223 "used in diesel engines for cars"
   224 "Quantity: 4 to 5 litres, in Plastic bottle"
   225 "Quality:Standard petrol,unleaded"
   226 "seven kilometers"
   227 "Shared Taxi,Fixed Route"
   228 "Fare for one student"
   229 "Return Ticket,six hours"
   230 "Big Ferry,Single Ticket"
   231 "Standard Airmail (e.g. to US, UK, etc.)"
   232 "Standard Letter,destination:National"
   233 "Home base telephone,Brandless"
   234 "Itel,simple phone"
   235 "1hour at cyber café"
   236 "E-Credit ('Nopal')"
   237 "4 GB"
   238 "Brand:LG,LED,32,SAMSUMG"
   239 "12 Meterband Radio"
   240 "4 Batteries/Memory USB"
   241 "GFF First division Leagues"
   242 "Premier league,Spanish League etc"
   243 "Saturday Night Club Ticket"
   244 "Senior Secondary Text Books (Grade 7)"
   245 "Newspaper (e.g. Standard,point,Forroya, etc.)"
   246 "International Magazines (News Week)"
   247 "Blue/Black/Red etc. ink pen"
   248 "Small Size"
   249 "Fees for a Student (Private School, Grade 7)"
   250 "Fees for per Student (Private School, Grade 9)"
   251 "1 plate e.g. Yassa,Domoda, Benachin etc."
   252 "1 cup of Tea with milk"
   253 "Three Star Hotel 1 Night"
   254 "Lodges Charges per Night without Air Conditioner"
   255 "Facial Makeup"
   256 "Hair cut"
   257 "Hair Straightening (Excluding Cost of Cream)"
   258 "Makeup powder (With Sponge and Mirror)"
   259 "Polish in small glass bottle container"
   260 "stainless Steel"
   261 "750ml,use for bath"
   262 "500ml"
   263 "200g,Santex"
   264 "For Adults,soft brush"
   265 "75ml,colgate"
   266 "Gold Immitation,Simple"
   267 "House built with cement"
   268 "one new Inner Tube"
;
label define RELEVANCE
     0 "Poor"
     1 "Nonpoor"
;
label define IMP_LOCAL
     1 "Imported"
     2 "Local"
;
label define COICOP06_ITEM_CODE
     1 "1.1.1.1"
     2 "1.1.1.2"
     3 "1.1.1.3"
     4 "1.1.1.4"
     5 "1.1.1.5"
     6 "1.1.1.6"
     7 "1.1.1.7"
     8 "1.1.1.8"
     9 "1.1.1.9"
    10 "1.1.1.10"
    11 "1.1.1.11"
    12 "1.1.1.12"
    13 "1.1.1.13"
    14 "1.1.1.14"
    15 "1.1.2.1"
    16 "1.1.2.2"
    17 "1.1.2.3"
    18 "1.1.2.4"
    19 "1.1.2.5"
    20 "1.1.2.6"
    21 "1.1.2.7"
    22 "1.1.3.1"
    23 "1.1.3.2"
    24 "1.1.3.3"
    25 "1.1.3.4"
    26 "1.1.3.5"
    27 "1.1.3.6"
    28 "1.1.3.7"
    29 "1.1.3.8"
    30 "1.1.3.9"
    31 "1.1.3.10"
    32 "1.1.4.1"
    33 "1.1.4.2"
    34 "1.1.4.3"
    35 "1.1.4.4"
    36 "1.1.4.5"
    37 "1.1.4.6"
    38 "1.1.4.7"
    39 "1.1.5.1"
    40 "1.1.5.2"
    41 "1.1.5.3"
    42 "1.1.5.4"
    43 "1.1.5.5"
    44 "1.1.6.1"
    45 "1.1.6.2"
    46 "1.1.6.3"
    47 "1.1.6.4"
    48 "1.1.6.5"
    49 "1.1.6.6"
    50 "1.1.6.7"
    51 "1.1.6.8"
    52 "1.1.6.9"
    53 "1.1.6.10"
    54 "1.1.6.11"
    55 "1.1.6.12"
    56 "1.1.7.1"
    57 "1.1.7.2"
    58 "1.1.7.3"
    59 "1.1.7.4"
    60 "1.1.7.5"
    61 "1.1.7.6"
    62 "1.1.7.7"
    63 "1.1.7.8"
    64 "1.1.7.9"
    65 "1.1.7.10"
    66 "1.1.7.11"
    67 "1.1.7.12"
    68 "1.1.7.13"
    69 "1.1.7.14"
    70 "1.1.7.15"
    71 "1.1.7.16"
    72 "1.1.7.17"
    73 "1.1.7.18"
    74 "1.1.7.19"
    75 "1.1.7.20"
    76 "1.1.8.1"
    77 "1.1.8.2"
    78 "1.1.8.3"
    79 "1.1.8.4"
    80 "1.1.8.5"
    81 "1.1.8.6"
    82 "1.1.8.7"
    83 "1.1.9.1"
    84 "1.1.9.2"
    85 "1.1.9.3"
    86 "1.1.9.4"
    87 "1.1.9.5"
    88 "1.1.9.6"
    89 "1.1.9.7"
    90 "1.1.9.8"
    91 "1.1.9.9"
    92 "1.2.1.1"
    93 "1.2.1.2"
    94 "1.2.1.3"
    95 "1.2.2.1"
    96 "1.2.2.2"
    97 "1.2.2.3"
    98 "1.2.2.4"
    99 "1.2.2.5"
   100 "2.1.1.1"
   101 "2.1.2.1"
   102 "2.1.2.2"
   103 "2.1.3.1"
   104 "2.1.3.2"
   105 "2.2.1"
   106 "2.2.2"
   107 "2.2.3"
   108 "2.2.4"
   109 "2.2.5"
   110 "2.2.6"
   111 "2.2.7"
   112 "2.3.1"
   113 "2.3.2"
   114 "3.1.1.1"
   115 "3.1.2.1"
   116 "3.1.2.2"
   117 "3.1.2.3"
   118 "3.1.2.4"
   119 "3.1.2.5"
   120 "3.1.2.6"
   121 "3.1.2.7"
   122 "3.1.2.8"
   123 "3.1.2.9"
   124 "3.1.2.10"
   125 "3.1.2.11"
   126 "3.1.2.12"
   127 "3.1.2.13"
   128 "3.1.2.14"
   129 "3.1.2.15"
   130 "3.1.2.16"
   131 "3.1.2.17"
   132 "3.1.2.18"
   133 "3.1.4.1"
   134 "3.2.1"
   135 "3.2.2"
   136 "3.2.3"
   137 "3.2.4"
   138 "3.2.5"
   139 "3.2.6"
   140 "3.2.7"
   141 "3.2.8"
   142 "3.2.9"
   143 "3.2.10"
   144 "3.2.11"
   145 "3.2.12"
   146 "4.1.1.1"
   147 "4.1.1.2"
   148 "4.1.1.3"
   149 "4.1.1.4"
   150 "4.1.1.5"
   151 "4.1.1.6"
   152 "4.3.1.1"
   153 "4.3.1.2"
   154 "4.3.1.3"
   155 "4.3.1.4"
   156 "4.3.1.5"
   157 "4.3.1.6"
   158 "4.3.1.7"
   159 "4.3.1.8"
   160 "4.3.1.9"
   161 "4.3.1.10"
   162 "4.4.1.1"
   163 "4.5.1"
   164 "4.5.2"
   165 "4.5.4.1"
   166 "4.5.4.2"
   167 "5.1.1.1"
   168 "5.1.1.2"
   169 "5.1.1.3"
   170 "5.1.1.4"
   171 "5.1.2.1"
   172 "5.2.1"
   173 "5.2.2"
   174 "5.2.3"
   175 "5.2.4"
   176 "5.3.1.1"
   177 "5.3.1.2"
   178 "5.3.1.3"
   179 "5.3.1.4"
   180 "5.3.1.5"
   181 "5.3.1.6"
   182 "5.4.1"
   183 "5.4.2"
   184 "5.4.3"
   185 "5.4.4"
   186 "5.4.5"
   187 "5.4.6"
   188 "5.4.7"
   189 "5.5.2.1"
   190 "5.5.2.2"
   191 "5.5.2.3"
   192 "5.6.1.1"
   193 "5.6.1.2"
   194 "5.6.1.3"
   195 "5.6.1.4"
   196 "5.6.1.5"
   197 "5.6.1.6"
   198 "5.6.1.7"
   199 "5.6.1.8"
   200 "5.6.1.9"
   201 "5.6.1.10"
   202 "5.6.2.1"
   203 "5.6.2.2"
   204 "6.1.1.1"
   205 "6.1.1.2"
   206 "6.1.1.3"
   207 "6.1.1.4"
   208 "6.1.1.5"
   209 "6.2.1"
   210 "6.2.1.1"
   211 "6.2.1.2"
   212 "6.2.2.1"
   213 "6.3.1"
   214 "7.1.1.1"
   215 "7.1.2.1"
   216 "7.1.3.1"
   217 "7.2.1.1"
   218 "7.2.1.2"
   219 "7.2.1.3"
   220 "7.2.1.4"
   221 "7.2.1.5"
   222 "7.2.1.6"
   223 "7.2.2.1"
   224 "7.2.2.2"
   225 "7.2.2.3"
   226 "7.3.2.1"
   227 "7.3.2.2"
   228 "7.3.2.3"
   229 "7.3.3.1"
   230 "7.3.4.1"
   231 "8.1.1"
   232 "8.1.2"
   233 "8.2.1"
   234 "8.2.2"
   235 "8.3.1"
   236 "8.3.2"
   237 "9.1.4.1"
   238 "9.2.2.1"
   239 "9.2.2.2"
   240 "9.2.2.3"
   241 "9.4.1.1"
   242 "9.4.2.1"
   243 "9.4.2.2"
   244 "9.5.1.4"
   245 "9.5.2.1"
   246 "9.5.2.2"
   247 "9.5.4.1"
   248 "9.5.4.2"
   249 "10.1"
   250 "10.2"
   251 "11.1.1.1"
   252 "11.1.1.2"
   253 "11.2.1"
   254 "11.2.2"
   255 "12.1.1.1"
   256 "12.1.1.2"
   257 "12.1.1.3"
   258 "12.1.3.1"
   259 "12.1.3.2"
   260 "12.1.3.3"
   261 "12.1.3.4"
   262 "12.1.3.5"
   263 "12.1.3.6"
   264 "12.1.3.7"
   265 "12.1.3.8"
   266 "12.3.1.1"
   267 "4.1.1.3"
   268 "7.2.1.2"
;
label define UOM     
     1 "4 cups"
     2 "4 cups"
     3 "4 cups"
     4 "4 cups"
     5 "1kg"
     6 "3 cups"
     7 "3 cups"
     8 "2 loaves"
     9 "3 ups"
    10 "3 cups"
    11 "1 packet"
    12 "3 pieces"
    13 "5 pieces"
    14 "500g"
    15 "1kg"
    16 "1 kg"
    17 "1 kg"
    18 "1 whole"
    19 "1 kg"
    20 "1 tin"
    21 "1kg"
    22 "3-4 whole"
    23 "3-4 whole"
    24 "3 whole"
    25 "3 whole"
    26 "3 whole"
    27 "3 cuts"
    28 "3 heaps"
    29 "1 heap"
    30 "1 Tin"
    31 "1 Tin"
    32 "1 Tin"
    33 "3 whole"
    34 "1 piece"
    35 "0.5 Liters"
    36 "0.5 Liters"
    37 "1 Tin"
    38 "1 Tin"
    39 "1 sacket"
    40 "1 jar"
    41 "1 liter"
    42 "250g"
    43 "1 liter"
    44 "6 bags"
    45 "1 cup"
    46 "3 cups"
    47 "3 Bags"
    48 "3-4 Pieces"
    49 "4 pieces"
    50 "4 pieces"
    51 "6 heaps"
    52 "4 Pieces"
    53 "1 cup"
    54 "1 whole"
    55 "1 whole"
    56 "3 bags"
    57 "3 bags"
    58 "1 kg"
    59 "3 heaps"
    60 "5 cuts"
    61 "500g"
    62 "5 heaps"
    63 "3 heaps"
    64 "6 pieces"
    65 "6 pieces"
    66 "6 heaps"
    67 "1 kg"
    68 "5 cuts"
    69 "3 heaps"
    70 "5 heaps"
    71 "3 heaps"
    72 "1 whole"
    73 "6 bundles"
    74 "1 tin/sacket"
    75 "3 pieces"
    76 "3 pieces"
    77 "5 bundles"
    78 "4 cups"
    79 "2 pieces"
    80 "1 packet"
    81 "500ml"
    82 "1 liter"
    83 "2 pieces"
    84 "70g-80g"
    85 "3 cups"
    86 "3 Wholes"
    87 "1 cube"
    88 "50 gram"
    89 "50 gram"
    90 "100g"
    91 "1 litre"
    92 "100g"
    93 "1 jar"
    94 "1 packet"
    95 "1 tin"
    96 "1 tin"
    97 "3 bags"
    98 "1 packet"
    99 "1 bottle"
   100 "1 bottle"
   101 "1 bottle"
   102 "1 bottle"
   103 "1 bottle"
   104 "1 liter"
   105 "1 bottle"
   106 "1 bottle"
   107 "1 packet"
   108 "1 packet"
   109 "1 packet"
   110 "50gr"
   111 "1 packet"
   112 "50gr"
   113 "1 packet"
   114 "5 pieces"
   115 "6 pieces"
   116 "1 metre"
   117 "1 piece"
   118 "1 piece"
   119 "1 piece"
   120 "1 piece"
   121 "1 piece"
   122 "1 piece"
   123 "1 piece"
   124 "1 piece"
   125 "1 piece"
   126 "1 piece"
   127 "1 piece"
   128 "1 piece"
   129 "1 piece"
   130 "1 piece"
   131 "1 piece"
   132 "1 piece"
   133 "1 pair"
   134 "1 unit"
   135 "1 service"
   136 "1 pair"
   137 "1 pair"
   138 "1 pair"
   139 "1 pair"
   140 "1 pair"
   141 "1 pair"
   142 "1 pair"
   143 "1 pair"
   144 "1 pair"
   145 "1 pair"
   146 "1 pair"
   147 "1 pair"
   148 "1 month"
   149 "1 month"
   150 "1 month"
   151 "1 month"
   152 "1 month"
   153 "1 month"
   154 "50kg"
   155 "1 square meter"
   156 "1 piece"
   157 "1 tin"
   158 "1 tin"
   159 "1 piece"
   160 "1 service"
   161 "1 service"
   162 "1 service"
   163 "1 piece"
   164 "1 month"
   165 "1 month"
   166 "1 bottle"
   167 "1 cup"
   168 "1 bundle"
   169 "1 unit"
   170 "1 unit"
   171 "1 set"
   172 "1 piece"
   173 "1 meter"
   174 "1 set"
   175 "1 piece"
   176 "1 piece"
   177 "1 piece"
   178 "1 piece"
   179 "1 piece"
   180 "1 piece"
   181 "1 piece"
   182 "1 unit"
   183 "1 piece"
   184 "1 piece"
   185 "1 piece"
   186 "1 piece"
   187 "1 piece"
   188 "1 piece"
   189 "1 piece"
   190 "1 piece"
   191 "1 pair"
   192 "1 piece"
   193 "1 piece"
   194 "1 liter"
   195 "1 piece"
   196 "1 piece"
   197 "1 piece"
   198 "1 box"
   199 "3 bags"
   200 "1 bar"
   201 "3 bags"
   202 "1 packet"
   203 "1 piece"
   204 "1 month"
   205 "1 month"
   206 "30 capsule"
   207 "24 Tablets"
   208 "24 Tablets"
   209 "30 Tablets"
   210 "10 tablets"
   211 "1 service"
   212 "1 service"
   213 "1 service"
   214 "1 service"
   215 "1 Adult"
   216 "1 car"
   217 "1 piece"
   218 " 1 piece"
   219 "1 piece"
   220 "1 piece"
   221 "1 service"
   222 "1 service"
   223 "1 piece"
   224 "1 piece"
   225 "1 litre"
   226 "1 bottle"
   227 "1 liter"
   228 "1 Ticket"
   229 "1 adult"
   230 "1 Ticket"
   231 "1 Ticket"
   232 "1 Ticket"
   233 "1 service"
   234 "1 service"
   235 " 1 piece"
   236 " 1 piece"
   237 "1 Hour"
   238 " 50 units"
   239 "1 piece"
   240 "1 piece"
   241 "1 piece"
   242 "1 Ticket"
   243 "1 Ticket"
   244 "1 Ticket"
   245 "1 piece"
   246 "1 piece"
   247 "1  piece"
   248 "1 piece"
   249 "1 piece"
   250 "1 Academic Year"
   251 "1 plate"
   252 "1 cup"
   253 "1 Night"
   254 "1 Night"
   255 "1 service"
   256 "1 service"
   257 "1 service"
   258 "1 piece"
   259 "1 piece"
   260 "1 piece"
   261 "1 piece"
   262 "1 piece"
   263 "1 piece"
   264 "1 piece"
   265 "1 piece"
   266 "1 piece"
   267 "1 month"
   268 "1 piece"
;
label define RCHECK  
     1 "Yes"
     2 "No"
;

#delimit cr
label values r_type   R_TYPE  
label values enumeration_market_em ENUMERATION_MARKET_EM
label values name_of_item NAME_OF_ITEM
label values discription_of_item DISCRIPTION_OF_ITEM
label values relevance RELEVANCE
label values imp_local IMP_LOCAL
label values coicop06_item_code COICOP06_ITEM_CODE
label values uom      UOM     
label values rcheck   RCHECK  
