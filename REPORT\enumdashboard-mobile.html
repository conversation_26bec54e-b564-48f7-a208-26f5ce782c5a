<!DOCTYPE html>
<html lang="en" class="light-style" dir="ltr" data-theme="theme-default" data-assets-path="../assets/" data-template="mobile-template">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=1.0, maximum-scale=3.0" />
    <title>Mobile Dashboard | Enumerator</title>
    <meta name="description" content="Mobile-optimized enumerator dashboard for CPI data collection" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="./assets/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet" />

    <!-- Icons -->
    <link rel="stylesheet" href="./assets/vendor/fonts/boxicons.css" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="./assets/vendor/css/core.css" class="template-customizer-core-css" />
    <link rel="stylesheet" href="./assets/vendor/css/theme-default.css" class="template-customizer-theme-css" />
    <link rel="stylesheet" href="./assets/css/demo.css" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="./assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />
    <link rel="stylesheet" href="./assets/vendor/libs/apex-charts/apex-charts.css" />

    <!-- Mobile-specific CSS -->
    <style>
        /* Mobile-first responsive design */
        .mobile-container {
            padding: 0.5rem;
            max-width: 100%;
            overflow-x: hidden;
        }

        .mobile-card {
            margin-bottom: 1rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .mobile-card:active {
            transform: scale(0.98);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 12px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card.success { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-card.warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .stat-card.danger { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); }
        .stat-card.info { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333; }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.85rem;
            opacity: 0.9;
        }

        .mobile-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .table-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }

        .case-item {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .case-item:last-child {
            border-bottom: none;
        }

        .case-info {
            flex: 1;
        }

        .case-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: #333;
        }

        .case-details {
            font-size: 0.8rem;
            color: #666;
        }

        .case-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-left: 0.5rem;
        }

        .status-completed { background: #d4edda; color: #155724; }
        .status-partial { background: #fff3cd; color: #856404; }
        .status-unavailable { background: #f8d7da; color: #721c24; }
        .status-not-found { background: #f8d7da; color: #721c24; }

        .action-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.5rem;
            border-radius: 8px;
            margin-left: 0.5rem;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .action-btn:hover {
            background: #0056b3;
        }

        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            border: none;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            cursor: pointer;
            z-index: 1000;
            transition: transform 0.2s ease;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
        }

        .header-mobile {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            margin: -0.5rem -0.5rem 1rem -0.5rem;
            text-align: center;
        }

        .header-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .header-subtitle {
            font-size: 0.85rem;
            opacity: 0.9;
        }

        /* Chart container for mobile */
        .mobile-chart {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }

        /* Toast notification for mobile */
        .mobile-toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #ff6b6b;
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1001;
            display: none;
        }

        /* Loading spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .case-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .case-status {
                margin-left: 0;
            }
        }
    </style>

    <!-- Helpers -->
    <script src="./assets/vendor/js/helpers.js"></script>
    <script src="./assets/js/config.js"></script>
</head>

<body>
    <div class="mobile-container">
        <!-- Header -->
        <div class="header-mobile">
            <div class="header-title">Enumerator Dashboard</div>
            <div class="header-subtitle">Report generated at ~~timestring()~~</div>
        </div>

        <!-- CSPro Data Processing -->
        <?
            numeric i=0;
            numeric SecondsInWeek = 60 * 60 * 24 * 7;
            numeric unsynced_count= countcases(CPI_CODEBOOK_DICT where synctime(CPI_CODEBOOK_DICT, "", uuid(CPI_CODEBOOK_DICT)) = notappl);
        ?>

        <?
            numeric Total_count = countcases(CPI_CODEBOOK_DICT);
            numeric partial_count = 0;
            numeric completed_count = 0;
            numeric tot_Refusal = 0;
            numeric not_found = 0;
            numeric others = 0;
            string partialCase, zdate, status;

            forcase CPI_CODEBOOK_DICT do
                if  !ispartial(CPI_CODEBOOK_DICT) and INTRODUCTION = 1 then
                inc(completed_count);
                endif;
            endfor;

            forcase CPI_CODEBOOK_DICT do
                if ispartial(CPI_CODEBOOK_DICT) then
                    inc(partial_count);
                    elseif INTERVIEW_RESULT = 3 then
                    inc(tot_Refusal);
                    elseif INTERVIEW_RESULT = 4 then
                    inc(not_found);
                    elseif INTERVIEW_RESULT = 98 then
                    inc(others);
                endif;
            endfor;
        ?>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card info">
                <div class="stat-number">~~Total_count~~</div>
                <div class="stat-label">Total Cases</div>
            </div>
            <div class="stat-card success">
                <div class="stat-number">~~completed_count~~</div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-number">~~partial_count~~</div>
                <div class="stat-label">Partial</div>
            </div>
            <div class="stat-card danger">
                <div class="stat-number">~~tot_Refusal~~</div>
                <div class="stat-label">Unavailable</div>
            </div>
            <div class="stat-card danger">
                <div class="stat-number">~~not_found~~</div>
                <div class="stat-label">Not Found</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-number">~~unsynced_count~~</div>
                <div class="stat-label">Unsynced</div>
            </div>
        </div>

        <!-- Performance Chart -->
        <div class="mobile-chart">
            <div class="chart-title">Daily Performance</div>
            <div id="mobilePerformanceChart" style="height: 200px;"></div>
        </div>

        <!-- Case Report Table -->
        <div class="mobile-table">
            <div class="table-header">
                <i class="bx bx-list-ul me-2"></i>Case Reports
            </div>

            <!-- Completed Cases -->
            <? forcase CPI_CODEBOOK_DICT do
                inc(i);
                if  !ispartial(CPI_CODEBOOK_DICT) and INTERVIEW_RESULT = 1 then
                    status = "Completed";
            ?>
            <div class="case-item">
                <div class="case-info">
                    <div class="case-name">~~strip(NAME_OF_OUTLET)~~</div>
                    <div class="case-details">
                        EM: ~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~ |
                        Date: ~~strip(INTERVIEW_START_DATE)~~
                    </div>
                </div>
                <div>
                    <span class="case-status status-completed">~~strip(status)~~</span>
                    <button class="action-btn" onclick="CSPro.runLogicAsync('modify_case(~~i~~);')" title="View">
                        <i class="bx bx-show"></i>
                    </button>
                </div>
            </div>
            <? endif;
            endfor; ?>

            <!-- Partial Cases -->
            <? forcase CPI_CODEBOOK_DICT do
                inc(i);
                if  ispartial(CPI_CODEBOOK_DICT) then
                    status = "Partial";
            ?>
            <div class="case-item">
                <div class="case-info">
                    <div class="case-name">~~strip(NAME_OF_OUTLET)~~</div>
                    <div class="case-details">
                        EM: ~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~ |
                        Date: ~~strip(INTERVIEW_START_DATE)~~
                    </div>
                </div>
                <div>
                    <span class="case-status status-partial">~~strip(status)~~</span>
                    <button class="action-btn" onclick="CSPro.runLogicAsync('modify_case(~~i~~);')" title="View">
                        <i class="bx bx-show"></i>
                    </button>
                </div>
            </div>
            <? endif;
            endfor; ?>

            <!-- Unavailable Cases -->
            <? forcase CPI_CODEBOOK_DICT do
                inc(i);
                if  !ispartial(CPI_CODEBOOK_DICT) and INTERVIEW_RESULT = 3 then
                    status = "Unavailable";
            ?>
            <div class="case-item">
                <div class="case-info">
                    <div class="case-name">~~strip(NAME_OF_OUTLET)~~</div>
                    <div class="case-details">
                        EM: ~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~ |
                        Date: ~~strip(INTERVIEW_START_DATE)~~
                    </div>
                </div>
                <div>
                    <span class="case-status status-unavailable">~~strip(status)~~</span>
                    <button class="action-btn" onclick="CSPro.runLogicAsync('modify_case(~~i~~);')" title="View">
                        <i class="bx bx-show"></i>
                    </button>
                </div>
            </div>
            <? endif;
            endfor; ?>

            <!-- Not Found Cases -->
            <? forcase CPI_CODEBOOK_DICT do
                inc(i);
                if  INTERVIEW_RESULT = 4 then
                    status = "Not Found";
            ?>
            <div class="case-item">
                <div class="case-info">
                    <div class="case-name">~~strip(NAME_OF_OUTLET)~~</div>
                    <div class="case-details">
                        EM: ~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~ |
                        Date: ~~strip(INTERVIEW_START_DATE)~~
                    </div>
                </div>
                <div>
                    <span class="case-status status-not-found">~~strip(status)~~</span>
                    <button class="action-btn" onclick="CSPro.runLogicAsync('modify_case(~~i~~);')" title="View">
                        <i class="bx bx-show"></i>
                    </button>
                </div>
            </div>
            <? endif;
            endfor; ?>
        </div>

        <!-- Refresh Button -->
        <button class="refresh-btn" onclick="location.reload()" title="Refresh Dashboard">
            <i class="bx bx-refresh"></i>
        </button>

        <!-- Toast Notification -->
        <div id="mobileToast" class="mobile-toast">
            <strong>Partial Cases!</strong> You have ~~partial_count~~ partial cases
        </div>
    </div>

    <!-- Core JS -->
    <script src="./assets/vendor/libs/jquery/jquery.js"></script>
    <script src="./assets/vendor/libs/popper/popper.js"></script>
    <script src="./assets/vendor/js/bootstrap.js"></script>
    <script src="./assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js"></script>
    <script src="./assets/vendor/js/menu.js"></script>

    <!-- Vendors JS -->
    <script src="./assets/vendor/libs/apex-charts/apexcharts.js"></script>

    <!-- Main JS -->
    <script src="./assets/js/main.js"></script>

    <!-- Mobile-specific JavaScript -->
    <script>
        // Mobile-optimized chart configuration
        document.addEventListener('DOMContentLoaded', function() {
            // Show toast notification if there are partial cases
            let partialCases = ~~partial_count~~;
            if (partialCases > 0) {
                showMobileToast();
            }

            // Initialize mobile performance chart
            initMobilePerformanceChart();

            // Add touch feedback for interactive elements
            addTouchFeedback();
        });

        function showMobileToast() {
            const toast = document.getElementById('mobileToast');
            toast.style.display = 'block';
            setTimeout(() => {
                toast.style.display = 'none';
            }, 5000);
        }

        function initMobilePerformanceChart() {
            const chartElement = document.querySelector('#mobilePerformanceChart');
            if (!chartElement) return;

            // Sample data - replace with actual CSPro data
            const options = {
                series: [{
                    name: 'Completed Cases',
                    data: [12, 19, 15, 27, 22, 18, 25]
                }],
                chart: {
                    type: 'area',
                    height: 200,
                    toolbar: { show: false },
                    sparkline: { enabled: false }
                },
                colors: ['#667eea'],
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.3,
                        stops: [0, 90, 100]
                    }
                },
                stroke: {
                    curve: 'smooth',
                    width: 2
                },
                xaxis: {
                    categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    labels: {
                        style: { fontSize: '12px' }
                    }
                },
                yaxis: {
                    labels: {
                        style: { fontSize: '12px' }
                    }
                },
                grid: {
                    borderColor: '#f0f0f0',
                    strokeDashArray: 3
                },
                dataLabels: { enabled: false },
                tooltip: {
                    theme: 'light',
                    style: { fontSize: '12px' }
                }
            };

            const chart = new ApexCharts(chartElement, options);
            chart.render();
        }

        function addTouchFeedback() {
            // Add haptic feedback for touch devices
            const interactiveElements = document.querySelectorAll('.action-btn, .refresh-btn, .stat-card');

            interactiveElements.forEach(element => {
                element.addEventListener('touchstart', function() {
                    if (navigator.vibrate) {
                        navigator.vibrate(50); // Short vibration
                    }
                });
            });
        }

        // Optimize for mobile performance
        function optimizeForMobile() {
            // Lazy load images
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        }

        // Initialize mobile optimizations
        optimizeForMobile();
    </script>
</body>
</html>
