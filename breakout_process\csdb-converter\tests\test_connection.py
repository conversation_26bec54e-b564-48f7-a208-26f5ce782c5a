"""
Tests for database connection functionality
"""
import pytest
from unittest.mock import <PERSON>M<PERSON>, patch

from src.core.database.connection import <PERSON><PERSON>ana<PERSON>, ConnectionProfile, DatabaseType, ConnectionStatus

def test_connection_profile_creation():
    """Test creating a connection profile"""
    profile = ConnectionProfile(
        name="Test Profile",
        db_type=DatabaseType.MYSQL,
        host="localhost",
        port=3306,
        database="test_db",
        username="test_user",
        password="test_pass"
    )
    
    assert profile.name == "Test Profile"
    assert profile.db_type == DatabaseType.MYSQL
    assert profile.host == "localhost"
    assert profile.port == 3306
    assert profile.database == "test_db"
    assert profile.username == "test_user"
    assert profile.password == "test_pass"

def test_connection_string_generation():
    """Test connection string generation"""
    # MySQL
    mysql_profile = ConnectionProfile(
        name="MySQL Test",
        db_type=DatabaseType.MYSQL,
        host="localhost",
        port=3306,
        database="test_db",
        username="test_user",
        password="test_pass"
    )
    assert mysql_profile.get_connection_string() == "mysql+pymysql://test_user:test_pass@localhost:3306/test_db"
    
    # PostgreSQL
    pg_profile = ConnectionProfile(
        name="PostgreSQL Test",
        db_type=DatabaseType.POSTGRESQL,
        host="localhost",
        port=5432,
        database="test_db",
        username="test_user",
        password="test_pass"
    )
    assert pg_profile.get_connection_string() == "postgresql://test_user:test_pass@localhost:5432/test_db"
    
    # SQL Server
    mssql_profile = ConnectionProfile(
        name="SQL Server Test",
        db_type=DatabaseType.SQLSERVER,
        host="localhost",
        port=1433,
        database="test_db",
        username="test_user",
        password="test_pass"
    )
    assert mssql_profile.get_connection_string() == "mssql+pyodbc://test_user:test_pass@localhost:1433/test_db?driver=ODBC+Driver+17+for+SQL+Server"
    
    # SQLite
    sqlite_profile = ConnectionProfile(
        name="SQLite Test",
        db_type=DatabaseType.SQLITE,
        database="test.db"
    )
    assert sqlite_profile.get_connection_string() == "sqlite:///test.db"

@patch('sqlalchemy.create_engine')
def test_connection_manager(mock_create_engine):
    """Test connection manager"""
    # Mock engine and connection
    mock_engine = MagicMock()
    mock_conn = MagicMock()
    mock_engine.connect.return_value.__enter__.return_value = mock_conn
    mock_create_engine.return_value = mock_engine
    
    # Create manager
    manager = ConnectionManager()
    
    # Add profile
    profile = ConnectionProfile(
        name="Test Profile",
        db_type=DatabaseType.SQLITE,
        database="test.db"
    )
    manager.add_profile(profile)
    
    assert len(manager.profiles) == 1
    assert manager.profiles[0].name == "Test Profile"
    
    # Connect
    result = manager.connect("Test Profile")
    assert result is True
    assert manager.status == ConnectionStatus.CONNECTED
    
    # Disconnect
    manager.disconnect()
    assert manager.status == ConnectionStatus.DISCONNECTED
    
    # Remove profile
    result = manager.remove_profile("Test Profile")
    assert result is True
    assert len(manager.profiles) == 0
