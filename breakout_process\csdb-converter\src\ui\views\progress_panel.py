"""
Progress and status panel
"""
import logging
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QProgressBar, QListWidget, QListWidgetItem, QGroupBox,
    QTextEdit
)
from PyQt6.QtCore import Qt, pyqtSlot, pyqtSignal

from src.ui.viewmodels.progress_viewmodel import ProgressViewModel
from src.core.processors.migration_processor import MigrationStatus
from src.ui.resources.resource_manager import ResourceManager

logger = logging.getLogger(__name__)

class ProgressPanel(QWidget):
    """Progress and status panel"""

    # Signals
    start_clicked = pyqtSignal()
    pause_clicked = pyqtSignal()
    cancel_clicked = pyqtSignal()

    def __init__(self, view_model: ProgressViewModel):
        super().__init__()

        self.view_model = view_model

        # Initialize UI
        self._init_ui()

        # Connect signals
        self._connect_signals()

        # Update UI
        self._update_ui()

    def _init_ui(self):
        """Initialize UI components"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(10)

        # Create group box
        group_box = QGroupBox("Progress and Status")
        main_layout.addWidget(group_box)

        # Create group layout
        group_layout = QVBoxLayout(group_box)

        # Create progress section
        progress_layout = QVBoxLayout()
        group_layout.addLayout(progress_layout)

        # Overall progress
        progress_layout.addWidget(QLabel("Overall Progress:"))
        self.overall_progress = QProgressBar()
        progress_layout.addWidget(self.overall_progress)

        # Current file progress
        progress_layout.addWidget(QLabel("Current File:"))
        self.file_label = QLabel("No file selected")
        progress_layout.addWidget(self.file_label)

        # Current table progress
        progress_layout.addWidget(QLabel("Current Table:"))
        self.table_label = QLabel("No table selected")
        progress_layout.addWidget(self.table_label)

        # Status messages
        progress_layout.addWidget(QLabel("Status:"))
        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setMaximumHeight(100)
        progress_layout.addWidget(self.status_text)

        # Button layout
        button_layout = QHBoxLayout()
        group_layout.addLayout(button_layout)

        # Create buttons
        self.start_button = QPushButton("Start Conversion")
        self.start_button.setIcon(ResourceManager.get_icon("start.png"))

        self.pause_button = QPushButton("Pause")
        self.pause_button.setIcon(ResourceManager.get_icon("pause.png"))

        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setIcon(ResourceManager.get_icon("stop.png"))

        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.pause_button)
        button_layout.addWidget(self.cancel_button)

        # Set initial button states
        self.pause_button.setEnabled(False)
        self.cancel_button.setEnabled(False)

    def _connect_signals(self):
        """Connect signals to slots"""
        # Connect view model signals
        self.view_model.progress_updated.connect(self._update_progress)
        self.view_model.status_message.connect(self._add_status_message)

        # Connect UI signals
        self.start_button.clicked.connect(self._on_start)
        self.pause_button.clicked.connect(self._on_pause)
        self.cancel_button.clicked.connect(self._on_cancel)

    def _update_ui(self):
        """Update UI state"""
        self._update_progress()

    @pyqtSlot()
    def _update_progress(self):
        """Update progress display"""
        # Update progress bar
        self.overall_progress.setValue(self.view_model.percentage)

        # Update progress format with additional information
        if self.view_model.is_running:
            # Format speed and time remaining
            records_per_sec = self.view_model.records_per_second
            time_remaining = self.view_model.estimated_time_remaining

            speed_text = f"{records_per_sec:.1f} records/sec"

            # Format time remaining
            if time_remaining > 0:
                minutes, seconds = divmod(int(time_remaining), 60)
                hours, minutes = divmod(minutes, 60)

                if hours > 0:
                    time_text = f"{hours}h {minutes}m {seconds}s"
                elif minutes > 0:
                    time_text = f"{minutes}m {seconds}s"
                else:
                    time_text = f"{seconds}s"

                self.overall_progress.setFormat(f"{self.view_model.percentage}% - {speed_text} - {time_text} remaining")
            else:
                self.overall_progress.setFormat(f"{self.view_model.percentage}% - {speed_text}")
        else:
            self.overall_progress.setFormat(f"{self.view_model.percentage}%")

        # Update file label
        if self.view_model.current_file:
            self.file_label.setText(self.view_model.current_file)
            self.file_label.setStyleSheet("color: #212121; font-weight: bold; padding: 4px; background-color: #F5F5F5; border-radius: 4px;")
        else:
            self.file_label.setText("No file selected")
            self.file_label.setStyleSheet("color: #757575; padding: 4px; background-color: #F5F5F5; border-radius: 4px;")

        # Update table label
        if self.view_model.current_table:
            self.table_label.setText(self.view_model.current_table)
            self.table_label.setStyleSheet("color: #212121; font-weight: bold; padding: 4px; background-color: #F5F5F5; border-radius: 4px;")
        else:
            self.table_label.setText("No table selected")
            self.table_label.setStyleSheet("color: #757575; padding: 4px; background-color: #F5F5F5; border-radius: 4px;")

        # Update button states based on status
        status = self.view_model.status

        if status == MigrationStatus.PENDING:
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.cancel_button.setEnabled(False)
            self.start_button.setText("Start Conversion")

        elif status == MigrationStatus.IN_PROGRESS:
            self.start_button.setEnabled(False)
            self.pause_button.setEnabled(True)
            self.cancel_button.setEnabled(True)
            self.pause_button.setText("Pause")

        elif status == MigrationStatus.PAUSED:
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.cancel_button.setEnabled(True)
            self.start_button.setText("Resume")

        elif status == MigrationStatus.COMPLETED:
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.cancel_button.setEnabled(False)
            self.start_button.setText("Start New Conversion")

        elif status == MigrationStatus.FAILED:
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.cancel_button.setEnabled(False)
            self.start_button.setText("Retry Conversion")

        elif status == MigrationStatus.CANCELLED:
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.cancel_button.setEnabled(False)
            self.start_button.setText("Start New Conversion")

    @pyqtSlot(str)
    def _add_status_message(self, message):
        """Add message to status text"""
        self.status_text.append(message)

        # Scroll to bottom
        scrollbar = self.status_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def _on_start(self):
        """Handle start/resume button click"""
        self.start_clicked.emit()

    def _on_pause(self):
        """Handle pause button click"""
        self.pause_clicked.emit()

    def _on_cancel(self):
        """Handle cancel button click"""
        self.cancel_clicked.emit()
