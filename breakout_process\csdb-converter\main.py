#!/usr/bin/env python3
"""
CSPro Database Converter
Main application entry point
"""
import sys
import logging
from PyQt6.QtWidgets import QApplication

from src.ui.views.main_window import MainWindow
from src.core.utils.logger import setup_logger

def main():
    """Main application entry point"""
    # Setup logging
    setup_logger()
    logger = logging.getLogger(__name__)
    logger.info("Starting CSPro Database Converter")
    
    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("CSPro Database Converter")
    app.setOrganizationName("Statistical Research")
    app.setOrganizationDomain("statresearch.org")
    
    # Create and show main window
    window = MainWindow()
    window.show()
    
    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
