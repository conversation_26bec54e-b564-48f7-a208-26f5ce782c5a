"""
Create a test CSDB file for testing
"""
import os
import struct

def create_test_csdb(file_path):
    """Create a simple test CSDB file"""
    with open(file_path, "wb") as f:
        # Write file signature
        f.write(b'CSDB')
        
        # Write version
        f.write(struct.pack("<I", 1))
        
        # Write dictionary name
        dict_name = "TestDictionary"
        f.write(struct.pack("<I", len(dict_name)))
        f.write(dict_name.encode('utf-8'))
        
        # Write dictionary label
        dict_label = "Test Dictionary for Testing"
        f.write(struct.pack("<I", len(dict_label)))
        f.write(dict_label.encode('utf-8'))
        
        # Write number of records (1)
        f.write(struct.pack("<I", 1))
        
        # Write record type
        record_type = "PERSON"
        f.write(struct.pack("<I", len(record_type)))
        f.write(record_type.encode('utf-8'))
        
        # Write record label
        record_label = "Person Record"
        f.write(struct.pack("<I", len(record_label)))
        f.write(record_label.encode('utf-8'))
        
        # Write number of fields (3)
        f.write(struct.pack("<I", 3))
        
        # Write field 1 (ID)
        field_name = "ID"
        f.write(struct.pack("<I", len(field_name)))
        f.write(field_name.encode('utf-8'))
        
        field_label = "Person ID"
        f.write(struct.pack("<I", len(field_label)))
        f.write(field_label.encode('utf-8'))
        
        # Data type (1 = Numeric)
        f.write(struct.pack("<I", 1))
        
        # Start position
        f.write(struct.pack("<I", 1))
        
        # Length
        f.write(struct.pack("<I", 4))
        
        # Occurrences
        f.write(struct.pack("<I", 1))
        
        # Write field 2 (NAME)
        field_name = "NAME"
        f.write(struct.pack("<I", len(field_name)))
        f.write(field_name.encode('utf-8'))
        
        field_label = "Person Name"
        f.write(struct.pack("<I", len(field_label)))
        f.write(field_label.encode('utf-8'))
        
        # Data type (2 = Alpha)
        f.write(struct.pack("<I", 2))
        
        # Start position
        f.write(struct.pack("<I", 5))
        
        # Length
        f.write(struct.pack("<I", 20))
        
        # Occurrences
        f.write(struct.pack("<I", 1))
        
        # Write field 3 (DOB)
        field_name = "DOB"
        f.write(struct.pack("<I", len(field_name)))
        f.write(field_name.encode('utf-8'))
        
        field_label = "Date of Birth"
        f.write(struct.pack("<I", len(field_label)))
        f.write(field_label.encode('utf-8'))
        
        # Data type (3 = Date)
        f.write(struct.pack("<I", 3))
        
        # Start position
        f.write(struct.pack("<I", 25))
        
        # Length
        f.write(struct.pack("<I", 8))
        
        # Occurrences
        f.write(struct.pack("<I", 1))
        
        # Write data section signature
        f.write(b'DATA')
        
        # Write number of records (2)
        f.write(struct.pack("<I", 2))
        
        # Write record 1
        f.write(struct.pack("<I", len(record_type)))
        f.write(record_type.encode('utf-8'))
        
        # ID
        f.write(b'1001')
        
        # NAME
        name = "John Doe"
        name = name.ljust(20)
        f.write(name.encode('utf-8'))
        
        # DOB
        f.write(b'19800101')
        
        # Write record 2
        f.write(struct.pack("<I", len(record_type)))
        f.write(record_type.encode('utf-8'))
        
        # ID
        f.write(b'1002')
        
        # NAME
        name = "Jane Smith"
        name = name.ljust(20)
        f.write(name.encode('utf-8'))
        
        # DOB
        f.write(b'19850215')

if __name__ == "__main__":
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.abspath(__file__)), exist_ok=True)
    
    # Create test file
    create_test_csdb(os.path.join(os.path.dirname(os.path.abspath(__file__)), "test.csdb"))
    print("Test CSDB file created successfully.")
