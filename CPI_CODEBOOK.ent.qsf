﻿---
fileType: Question Text
version: CSPro 7.7
languages:
  - name: EN
    label: English
styles:
  - name: Normal
    className: normal
    css: |
      font-family: Arial;font-size: 16px;
  - name: Instruction
    className: instruction
    css: |
      font-family: Arial;font-size: 14px;color: #0000FF;
  - name: Heading 1
    className: heading1
    css: |
      font-family: Arial;font-size: 36px;
  - name: Heading 2
    className: heading2
    css: |
      font-family: Arial;font-size: 24px;
  - name: Heading 3
    className: heading3
    css: |
      font-family: Arial;font-size: 18px;
questions:
  - name: CPI_CODEBOOK_DICT.R_TYPE
    conditions:
      - questionText:
          EN: |
            <p class="15"><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;">SECTION A. IDENTIFICATION OF OUTLET</span></i></p><p class="15"><span style="font-family: &quot;Times New Roman&quot;; font-size: 12pt;">1.1. Area&nbsp;</span></p><p></p>
  - name: CPI_CODEBOOK_DICT.ENUMERATION_MARKET_EM
    conditions:
      - questionText:
          EN: |
            <p class="15"><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION A. IDENTIFICATION OF OUTLET</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p class="15">Select Market Enumeration:</p><p class="15"><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><o:p></o:p></span></i></p><p></p>
  - name: CPI_CODEBOOK_DICT.LGA
    conditions:
      - questionText:
          EN: |
            <p class="15"><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION A. IDENTIFICATION OF OUTLET</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p class="15">SELECT LGA:</p><p></p>
  - name: CPI_CODEBOOK_DICT.DISTRICT_CODE
    conditions:
      - questionText:
          EN: |
            <p class="15"><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION A. IDENTIFICATION OF OUTLET</b></span></i>&nbsp; &nbsp;&nbsp;</p><p class="15">SELECT DISTRICT CODE:</p><p class="15"><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><o:p></o:p></span></i></p><p></p>
  - name: CPI_CODEBOOK_DICT.DISTRICT__NAME
    conditions:
      - questionText:
          EN: |
            <p class="15"><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION A. IDENTIFICATION OF OUTLET</b></span></i></p><p class="15"></p><p class="15"><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><o:p></o:p></span></i></p><p></p>
  - name: CPI_CODEBOOK_DICT.SETTLEMENT
    conditions:
      - questionText:
          EN: |
            <p class="15"><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION A. IDENTIFICATION OF OUTLET</b></span></i><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><o:p></o:p></span></i></p><p></p>
  - name: CPI_CODEBOOK_DICT.INTRODUCTION
    conditions:
      - questionText:
          EN: |
            <p class="15"><span style="font-family: &quot;Times New Roman&quot;; font-size: 12pt;"><font color="#4472c4" style="font-style: italic;">SECTION A. IDENTIFICATION OF OUTLET</font><br>INTRODUCTION:</span></p><p class="MsoNormal" align="justify" style="text-align:justify;text-justify:inter-ideograph;"><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'Times New Roman';
            mso-bidi-font-family:'Times New Roman';font-size:12.0000pt;mso-font-kerning:0.0000pt;">Dear Sir/Madam, my name is (Name), I work for the Gambia Bureau of Statistics (GBoS) as a price data collector. The purpose of the survey is to register outlet or entities in The Gambia where price is collected in every month, to keep records of our collection points. &nbsp;The Data collected will help the Bureau to keep track of all the outlet that price is collected to make reference wherein we needed clarification and replacement since not all outlet are visited during our monthly visits. &nbsp;The interview will last at least 10 minutes. The Statistics Act of 2005 empowers GBoS to collect information for statistical purposes as well as protect its reproduction and dissemination that reveals individual information to third parties. You are therefore assured of our adherence to this code of confidentiality. </span><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'Times New Roman';
            mso-bidi-font-family:'Times New Roman';font-size:12.0000pt;mso-font-kerning:0.0000pt;"><o:p></o:p></span></p><p class="MsoNormal" align="justify" style="text-align:justify;text-justify:inter-ideograph;"><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:'Times New Roman';
            mso-bidi-font-family:'Times New Roman';font-size:12.0000pt;mso-font-kerning:0.0000pt;" class="instruction"><i>Can I continue?</i></span></p><p></p>
  - name: CPI_CODEBOOK_DICT.INTERVIEW_RESULT
    conditions:
      - questionText:
          EN: |
            INTERVIEW RESULTS:<p></p>
  - name: CPI_CODEBOOK_DICT.NAME_OF_OUTLET
    conditions:
      - questionText:
          EN: |
            <p class="15"><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>OUTLET INFORMATION &amp; CONTACT PERSON’S DETAILS</b></span></i></p><p class="15"><span style="font-family: &quot;Times New Roman&quot;; font-size: 12pt;">Enter Outlet Name:</span></p><p></p>
  - name: CPI_CODEBOOK_DICT.NAME_OF_ITEM
    conditions:
      - questionText:
          EN: |
            <p class="15"><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION A. IDENTIFICATION OF OUTLET</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p class="15">1.7. NAME OF ITEM:</p><p class="15"><span class="instruction"><i>Select Item from the list</i></span></p><p class="15"><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><o:p></o:p></span></i></p><p></p>
  - name: CPI_CODEBOOK_DICT.DISCRIPTION_OF_ITEM
    conditions:
      - questionText:
          EN: |
            <p>&nbsp; 1.8. ITEM DISCRIPTION:</p><p></p>
  - name: CPI_CODEBOOK_DICT.RELEVANCE
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION A. IDENTIFICATION OF OUTLET</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p><p>1.9. RELEVENCE:</p></p>
  - name: CPI_CODEBOOK_DICT.IMP_LOCAL
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION A. IDENTIFICATION OF OUTLET</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p><p>1.10. What is the origin of the item?</p></p>
  - name: CPI_CODEBOOK_DICT.COICOP06_ITEM_CODE
    conditions:
      - questionText:
          EN: |
            <p>1.11. COICOP (ITEM CODE):</p><p><br></p><p></p><p></p><p></p>
  - name: CPI_CODEBOOK_DICT.UOM
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION A. IDENTIFICATION OF OUTLET</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p><p>1.12. What is the unit of measurement (UoM)?</p></p>
  - name: CPI_CODEBOOK_DICT.QTY202101
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION A. IDENTIFICATION OF OUTLET</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p><p>1.13. What is the quantity?</p></p>
  - name: CPI_CODEBOOK_DICT.PRICE202101
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION A. IDENTIFICATION OF OUTLET</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p><p>1.14. What is the price?</p></p>
  - name: CPI_CODEBOOK_DICT.RCHECK
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION A. IDENTIFICATION OF OUTLET</b></span></i>&nbsp;&nbsp;&nbsp;</p><p>You've list the following items:<br></p><div style="text-align: center;">~~~capi_items_name_price~~~</div><p></p><p></p><p>Are there any other items you want to add ?</p><p></p>
  - name: CPI_CODEBOOK_DICT.TYPE_OF_OUTLET
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION B: OUTLET INFORMATION &amp; CONTACT PERSON’S DETAILS</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p><p>1.16. Outlet Type:</p><p><br></p></p>
  - name: CPI_CODEBOOK_DICT.PHONE_NUMBER_OF_OUTLET
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION B: OU</b></span></i><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>TLET INFORMATION &amp; CONTACT PERSON’S DETAILS</b></span></i>&nbsp; &nbsp; &nbsp; &nbsp;</p><p>1.17. Enter ~~NAME_OF_OUTLET~~'s 1st Phone number:</p><p></p><p></p>
  - name: CPI_CODEBOOK_DICT.ND_PHONE_NUMBER_OF_OUTLET
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION B: OUTLET INFORMATION &amp; CONTACT PERSON’S DETAILS</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p></p><p>1.17. Enter ~~NAME_OF_OUTLET~~'s 2nd Phone Number:</p><p><br></p><p><span class="instruction"><i>(input 0 if not exist)</i></span></p><p></p>
  - name: CPI_CODEBOOK_DICT.EMAIL_ADDRESS_OF_OUTLET
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION B: OUTLET INFORMATION &amp; CONTACT PERSON’S DETAILS</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p></p><p>1.8. Outlet's Email address:</p><p><i style="color: rgb(0, 0, 255); font-size: 14px;">leave blank if it doesn't exist</i></p><p></p>
  - name: CPI_CODEBOOK_DICT.STREET_ADDRESS_OF_OUTLET
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION B: OUTLET INFORMATION &amp; CONTACT PERSON’S DETAILS</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p><p>1.9. Street Address of Outlet:</p></p>
  - name: CPI_CODEBOOK_DICT.CONTACT_PERSON_NAME
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION B: OUTLET INFORMATION &amp; CONTACT PERSON’S DETAILS</b></span></i>&nbsp; &nbsp;&nbsp;</p><p>2.1. Contact Person Name:</p><p><p></p></p>
  - name: CPI_CODEBOOK_DICT.CONTACT_PERSON_EMAIL
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION B: OUTLET INFORMATION &amp; CONTACT PERSON’S DETAILS</b></span></i>&nbsp; &nbsp;</p><p></p><p>2.2. What is&nbsp;<i style="color: rgb(231, 99, 99);">~~CONTACT_PERSON_NAME~~&nbsp; </i>Email?</p><p><br></p><p><span class="instruction"><i>leave blank if it doesn't exist</i></span></p><p></p>
  - name: CPI_CODEBOOK_DICT.POSITION
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION B: OUTLET INFORMATION &amp; CONTACT PERSON’S DETAILS</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p></p><p>2.3. What is&nbsp;<i style="color: rgb(231, 99, 99);">~~CONTACT_PERSON_NAME~~'s&nbsp;</i>position in <i><font color="#ce0000">~~NAME_OF_OUTLET~~</font></i> outlet:</p><p></p>
  - name: CPI_CODEBOOK_DICT.OTHER_SPECIFY_POSITION
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION B: OUTLET INFORMATION &amp; CONTACT PERSON’S DETAILS</b></span></i>&nbsp; &nbsp;&nbsp;</p><p></p><p>Please Specify the Position of&nbsp;<i style="color: rgb(231, 99, 99);">~~CONTACT_PERSON_NAME~~:</i></p><p></p>
  - name: CPI_CODEBOOK_DICT.PHONE_NUMBER_1
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION B: OUTLET INFORMATION &amp; CONTACT PERSON’S DETAILS</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p>2.4. Phone Number of <font color="#e76363"><i>~~CONTACT_PERSON_NAME~~</i></font></p><p><br></p><p></p><p></p><p></p>
  - name: CPI_CODEBOOK_DICT.PHONE_NUMBER_2
    conditions:
      - questionText:
          EN: |
            <p><i><span style="font-family: &quot;Times New Roman&quot;; color: rgb(68, 114, 196); font-size: 12pt;"><b>SECTION B: OUTLET INFORMATION &amp; CONTACT PERSON’S DETAILS</b></span></i>&nbsp;&nbsp;&nbsp;&nbsp;</p><p>2.4. 2nd Phone Number of&nbsp;<i style="color: rgb(231, 99, 99);">~~CONTACT_PERSON_NAME~~</i></p><p><span class="instruction">﻿</span><br></p><p><i style="color: rgb(0, 0, 255); font-size: 14px;">(input 0 if not exist)</i></p><p></p><p></p><p></p>
  - name: CPI_CODEBOOK_DICT.GPS_READING
    conditions:
      - questionText:
          EN: |
            Collect GPS Reading:<p></p>
  - name: CPI_CODEBOOK_DICT.NAME_OF_ITEM1
    conditions:
      - questionText:
          EN: |
            1.7. NAME OF ITEM:<p></p>
...
