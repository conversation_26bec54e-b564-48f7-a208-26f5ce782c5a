"""
Configuration view model
"""
import logging
from typing import Dict
from PyQt6.QtCore import QObject, pyqtSignal

logger = logging.getLogger(__name__)

class ConfigViewModel(QObject):
    """View model for conversion configuration"""
    
    # Signals
    config_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        
        # Configuration values
        self._table_prefix = ""
        self._batch_size = 1000
        self._encoding = "utf-8"
        self._max_concurrent = 3
        
        # Data type mapping
        self._type_mapping = {
            "numeric": "INTEGER",
            "alpha": "VARCHAR",
            "date": "DATE"
        }
    
    @property
    def table_prefix(self) -> str:
        """Get table prefix"""
        return self._table_prefix
    
    def set_table_prefix(self, prefix: str) -> None:
        """Set table prefix"""
        if self._table_prefix != prefix:
            self._table_prefix = prefix
            self.config_changed.emit()
            logger.debug(f"Table prefix set to: {prefix}")
    
    @property
    def batch_size(self) -> int:
        """Get batch size"""
        return self._batch_size
    
    def set_batch_size(self, size: int) -> None:
        """Set batch size"""
        if self._batch_size != size:
            self._batch_size = size
            self.config_changed.emit()
            logger.debug(f"Batch size set to: {size}")
    
    @property
    def encoding(self) -> str:
        """Get character encoding"""
        return self._encoding
    
    def set_encoding(self, encoding: str) -> None:
        """Set character encoding"""
        if self._encoding != encoding:
            self._encoding = encoding
            self.config_changed.emit()
            logger.debug(f"Encoding set to: {encoding}")
    
    @property
    def max_concurrent(self) -> int:
        """Get maximum concurrent conversions"""
        return self._max_concurrent
    
    def set_max_concurrent(self, count: int) -> None:
        """Set maximum concurrent conversions"""
        if self._max_concurrent != count:
            self._max_concurrent = count
            self.config_changed.emit()
            logger.debug(f"Max concurrent conversions set to: {count}")
    
    def get_type_mapping(self, csdb_type: str) -> str:
        """Get SQL type for CSPro type"""
        return self._type_mapping.get(csdb_type, "")
    
    def set_type_mapping(self, csdb_type: str, sql_type: str) -> None:
        """Set SQL type for CSPro type"""
        if csdb_type in self._type_mapping and self._type_mapping[csdb_type] != sql_type:
            self._type_mapping[csdb_type] = sql_type
            self.config_changed.emit()
            logger.debug(f"Type mapping set: {csdb_type} -> {sql_type}")
    
    def get_all_type_mappings(self) -> Dict[str, str]:
        """Get all type mappings"""
        return self._type_mapping.copy()
