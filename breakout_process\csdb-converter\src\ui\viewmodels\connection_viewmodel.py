"""
Connection view model
"""
import logging
import json
import os
from typing import List, Dict, Optional
from PyQt6.QtCore import QObject, pyqtSignal

from src.core.database.connection import ConnectionManager, ConnectionProfile, ConnectionStatus, DatabaseType
from src.config.settings import Settings

logger = logging.getLogger(__name__)

class ConnectionViewModel(QObject):
    """View model for database connection management"""

    # Signals
    profiles_changed = pyqtSignal()
    connection_status_changed = pyqtSignal()

    def __init__(self):
        super().__init__()

        # Create connection manager
        self.connection_manager = ConnectionManager()

        # Load saved profiles
        self._load_profiles()

    @property
    def profiles(self) -> List[ConnectionProfile]:
        """Get all connection profiles"""
        return self.connection_manager.profiles

    @property
    def current_profile(self) -> Optional[ConnectionProfile]:
        """Get current connection profile"""
        return self.connection_manager.current_profile

    @property
    def status(self) -> ConnectionStatus:
        """Get connection status"""
        return self.connection_manager.status

    @property
    def error_message(self) -> str:
        """Get error message"""
        return self.connection_manager.error_message

    @property
    def is_connected(self) -> bool:
        """Check if connected to database"""
        return self.status == ConnectionStatus.CONNECTED

    def load_profiles(self, profile_data: List[Dict]) -> None:
        """Load connection profiles from settings"""
        for data in profile_data:
            profile = ConnectionProfile.from_dict(data)
            self.connection_manager.add_profile(profile)

        self.profiles_changed.emit()
        logger.info(f"Loaded {len(profile_data)} connection profiles")

    def add_profile(self, profile: ConnectionProfile) -> None:
        """Add a new connection profile"""
        self.connection_manager.add_profile(profile)
        self.profiles_changed.emit()
        logger.info(f"Added connection profile: {profile.name}")

        # Save profiles
        self._save_profiles()

    def remove_profile(self, name: str) -> bool:
        """Remove a connection profile by name"""
        result = self.connection_manager.remove_profile(name)
        self.profiles_changed.emit()
        self.connection_status_changed.emit()
        logger.info(f"Removed connection profile: {name}")

        # Save profiles
        self._save_profiles()

        return result

    def get_profile(self, name: str) -> Optional[ConnectionProfile]:
        """Get a connection profile by name"""
        return self.connection_manager.get_profile(name)

    def set_current_profile(self, name: str) -> None:
        """Set current profile by name"""
        profile = self.get_profile(name)
        if profile:
            self.connection_manager._current_profile = profile
            self.connection_status_changed.emit()

    def connect(self, profile_name: str) -> bool:
        """Connect to database using the specified profile"""
        result = self.connection_manager.connect(profile_name)
        self.connection_status_changed.emit()

        if result:
            logger.info(f"Connected to database: {profile_name}")
        else:
            logger.error(f"Connection failed: {self.error_message}")

        return result

    def disconnect(self) -> None:
        """Disconnect from database"""
        self.connection_manager.disconnect()
        self.connection_status_changed.emit()
        logger.info("Disconnected from database")

    def test_connection(self, profile: ConnectionProfile) -> bool:
        """Test connection to database"""
        return self.connection_manager.test_connection(profile)

    def _load_profiles(self) -> None:
        """Load connection profiles from settings"""
        try:
            # Get profiles path
            profiles_path = os.path.join(Settings.get_app_data_dir(), "connection_profiles.json")

            # Check if file exists
            if not os.path.exists(profiles_path):
                logger.info("No saved connection profiles found")
                return

            # Load profiles from file
            with open(profiles_path, "r") as f:
                profile_data = json.load(f)

            # Create profiles
            for data in profile_data:
                profile = ConnectionProfile.from_dict(data)
                self.connection_manager.add_profile(profile)

            logger.info(f"Loaded {len(profile_data)} connection profiles")

        except Exception as e:
            logger.error(f"Error loading connection profiles: {e}")

    def _save_profiles(self) -> None:
        """Save connection profiles to settings"""
        try:
            # Get profiles path
            profiles_dir = Settings.get_app_data_dir()
            os.makedirs(profiles_dir, exist_ok=True)
            profiles_path = os.path.join(profiles_dir, "connection_profiles.json")

            # Convert profiles to dict
            profile_data = [profile.to_dict() for profile in self.profiles]

            # Save profiles to file
            with open(profiles_path, "w") as f:
                json.dump(profile_data, f, indent=4)

            logger.info(f"Saved {len(profile_data)} connection profiles")

        except Exception as e:
            logger.error(f"Error saving connection profiles: {e}")
