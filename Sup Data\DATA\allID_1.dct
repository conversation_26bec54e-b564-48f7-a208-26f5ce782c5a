infix dictionary using "C:\Users\<USER>\Desktop\CPI_CODEBOOK\Sup Data\DATA\allID_1.dat" {
1 lines

    str      outlet_uuid    1:   1-12  
    byte     r_type      1:  13-13  
    byte     enumeration_market_em    1:  14-15  
    byte     lga         1:  16-16  
    byte     district_code    1:  17-18  
    str      district__name    1:  19-63  
    int      settlement    1:  64-66  
    byte     introduction    1:  67-67  
    byte     interview_result    1:  68-68  
    str      name_of_outlet    1:  69-293 
    byte     type_of_outlet    1: 294-294 
    long     phone_number_of_outlet    1: 295-301 
    long     nd_phone_number_of_outlet    1: 302-308 
    str      email_address_of_outlet    1: 309-353 
    str      street_address_of_outlet    1: 354-398 
    str      contact_person_name    1: 399-413 
    str      contact_person_email    1: 414-458 
    byte     position    1: 459-459 
    str      other_specify_position    1: 460-519 
    long     phone_number_1    1: 520-526 
    long     phone_number_2    1: 527-533 
    str      interviewer_code    1: 534-540 
    str      interviewer_name    1: 541-590 
    str      interview_start_date    1: 591-600 
    str      interview_start_time    1: 601-610 
    str      interview_end_time    1: 611-620 
}
