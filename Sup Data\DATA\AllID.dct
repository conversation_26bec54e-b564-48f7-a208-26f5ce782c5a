infix dictionary using "C:\Users\<USER>\Desktop\CPI_CODEBOOK\Sup Data\DATA\allID.dat" {
3 lines
    str      outlet_uuid    1:   1-12  
    byte     r_type      1:  13-13  
    byte     enumeration_market_em    1:  14-15  
    str      REC_TYPE    1:  16-16  
    byte     lga         1:  17-17  
    byte     district_code    1:  18-19  
    str      district__name    1:  20-64  
    int      settlement    1:  65-67  
    byte     introduction    1:  68-68  
    byte     interview_result    1:  69-69  

    str      name_of_outlet    2:  17-241 
    byte     type_of_outlet    2: 242-242 
    long     phone_number_of_outlet    2: 243-249 
    long     nd_phone_number_of_outlet    2: 250-256 
    str      email_address_of_outlet    2: 257-301 
    str      street_address_of_outlet    2: 302-346 
    str      contact_person_name    2: 347-361 
    str      contact_person_email    2: 362-406 
    byte     position    2: 407-407 
    str      other_specify_position    2: 408-467 
    long     phone_number_1    2: 468-474 
    long     phone_number_2    2: 475-481 

    str      interviewer_code    3:  17-23  
    str      interviewer_name    3:  24-73  
    str      interview_start_date    3:  74-83  
    str      interview_start_time    3:  84-93  
    str      interview_end_time    3:  94-103 
}
