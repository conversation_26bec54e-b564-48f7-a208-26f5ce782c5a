# CSPro Database Converter

A specialized tool designed to facilitate the migration of CSPro survey databases (CSDB) to modern SQL database systems.

## Features

- **Database Connection Management**
  - Support for MySQL, PostgreSQL, SQL Server, and SQLite
  - Connection profile management
  - Secure credential storage

- **CSDB File Processing**
  - Multi-file selection support
  - Drag-and-drop functionality
  - File validation and structure preview

- **Conversion Configuration**
  - Custom table name prefixing
  - Data type mapping
  - Character encoding selection

- **Data Migration**
  - Batch processing capability
  - Progress tracking
  - Pause/Resume functionality
  - Error handling and logging

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/csdb-converter.git
   cd csdb-converter
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # Unix
   source venv/bin/activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

1. Run the application:
   ```bash
   python main.py
   ```

2. Configure database connection:
   - Add a new connection profile
   - Test the connection
   - Connect to the database

3. Select CSDB files:
   - Use the Browse button or drag and drop files
   - Files will be validated automatically

4. Configure conversion settings:
   - Set table prefix (optional)
   - Configure data type mappings
   - Set batch size and encoding

5. Start conversion:
   - Click the "Start Conversion" button
   - Monitor progress in the status area
   - View results when complete

## Development

### Project Structure

```
csdb-converter/
├── src/
│   ├── ui/
│   │   ├── views/
│   │   ├── viewmodels/
│   │   └── resources/
│   ├── core/
│   │   ├── processors/
│   │   ├── database/
│   │   └── utils/
│   └── config/
├── tests/
├── docs/
└── resources/
```

### Running Tests

```bash
pytest
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- CSPro (Census and Survey Processing System) by the U.S. Census Bureau
- SQLAlchemy ORM
- PyQt6 UI framework
