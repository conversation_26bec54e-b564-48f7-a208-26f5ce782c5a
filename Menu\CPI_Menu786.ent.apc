﻿{Application 'CPI_MENU786' logic file generated by CSPro}
PROC GLOBAL

//Define global variables for paths
string Path<PERSON>rog, pathData, PathInput, PathMaps, PathIcons, PathResources, Pathreports, temp_path;
string cPassword;
string capi_publish_date;
string owner_name, current_version, syslogSUP, syslogEnumerator;
config gb_app_version;
hashmap string HashAgentName;
string CurrentUserName, TeamCode;
string date;
list string caseIndex;
list string agent_HD;
file pffFile;
numeric sync_count, team, partial_case;
numeric unsynced_cases = 0;
array string team_array(20,3);
numeric start = 1;
string QRlogin, QRstring;
string uuidcode,uuidpass;
string datetimesys;

list numeric dailyCounts;  // A list to hold the count for each day
    numeric j, countCompleted, targetDate;
//FUNCTION TO OVERWRITE THE BACK BUTTON WHEN EXITING THE APPLICATION
function OnStop()
	numeric response = errmsg("Do you want exit?")
		select("Yes", continue, "No", continue);
	if response = 2 then
		// Cancel
		reenter;
	elseif response = 1 then
		stop(1); // close the program
	endif;
end;

//House hold  *****************************************
function open_form(string idForm)
	pff form_pff;
	//form_pff.setproperty("operatorid", choice_id_actor);
	form_pff.setproperty("StartMode", "ADD");
	form_pff.setproperty("Application", "../CPI_CODEBOOK.pen");
	form_pff.setproperty("InputData","../Data/CPI_CODEBOOK.csdb");
	form_pff.setproperty("Description", "Edit Form");
	form_pff.setproperty("key", idForm);

	//external dictionnary
	form_pff.setproperty("ExternalFiles.STAFF_DICT", "./data/staff.csdb");
	form_pff.setproperty("ExternalFiles.EXT_STR_DICT","../Data/CPI_CODEBOOK.csdb");
	form_pff.setproperty("OnExit", "../menu/CPI_Menu786.pff");
	form_pff.exec();
end;

//Launch function 
function modify_case(i)
	string keyfolder = agent_HD(i);
	if ! loadcase(CPI_CODEBOOK_DICT, keyFolder) then 
		errmsg("Unable to load the informations for this agent.");
		exit;
	endif;
	open_form(keyfolder);
end;

//backup
function localBackup()
	//local backup goes here

end;

//send to server
function sendToServer()
	if ! connection() then
		errmsg("No internet connection available");
		reenter;
	endif;

	if ! connect_to_server()  then
		errmsg("Cannot reach the server.");
		reenter;
	endif;

	//Sync Form if data exist
	if ! syncdata(PUT,CPI_CODEBOOK_DICT) then
		syncdisconnect();
		reenter;
	endif;
	errmsg("operation successful");
	reenter;

end;


//logout function
function logout()
	errmsg ("This will clear current credentials. Do you want to continue ?")
	select("Yes",continue, "No", reenter);
	savesetting(clear);
	stop(1);
	
end;
//sync with supervisor
function syncWithSupervisor()
	// Do bluetooth synchronization with supervisor
	if syncconnect(Bluetooth)= 1 then

		// Connection worked
		// Upload household data
		if ! syncdata(PUT, CPI_CODEBOOK_DICT) then
			errmsg("Unable to send users data to the Supervisor.");
		endif;
		
		if ! syncdata(PUT, STAFF_DICT) then 
			errmsg("Unable to send users data to the Supervisor.");
		endif;
		// disconnect
		syncdisconnect();
		errmsg("Sync Successfull!");
		else 
		errmsg("Failed to Sync. Try again");
	endif;

end;

//sync with enum
function syncWithInterviewer()

	// Allow interviewer tablet to
	// connect for synchronization
	//bluetooth config
	if getbluetoothName()<>maketext("%s",strip(S_NAME)) then 
		setbluetoothName(maketext("%s",strip(S_NAME)));
	endif;
	
		if syncserver(bluetooth) then
		errmsg("Data received successfully");
	else
		errmsg("Error connecting with the enumerator.");
		syncdisconnect();
		reenter;
	endif;
	reenter;

end;


function launchDataEntry()
	// Run Questionnaire program
	string pffFilename = pathname(application) + "CPI_CODEBOOK.pff";
	setfile(pffFile,pffFilename,create);

	filewrite(pffFile,"[Run Information]");
	 filewrite(pffFile,"Version=CSPro 7.7");
	filewrite(pffFile,"AppType=Entry");
	
	filewrite(pffFile,"[DataEntryInit]");
	filewrite(pffFile,"ShowInApplicationListing=Hidden");
	filewrite(pffFile,"StartMode=add");
	filewrite(pffFile,"Lock=CaseListing");

	filewrite(pffFile,"[Files]");
	filewrite(pffFile,"Application=../CPI_CODEBOOK.ent");
	filewrite(pffFile,"InputData=../Data/CPI_CODEBOOK.csdb|CSPRODB");
	filewrite(pffFile,"Paradata=./CPI_CODEBOOK.cslog");

	filewrite(pffFile,"[ExternalFiles]");
	filewrite(pffFile,"[Parameters]");

	filewrite(pffFile,"OnExit=%s","../Menu/CPI_Menu786.pff");
	filewrite(pffFile,"INTERVIEWER_CODE=%v", S_CODE);
	filewrite(pffFile,"INTERVIEWER_NAME=%v", S_NAME );
	filewrite(pffFile,"INTERVIEWER_PHONE=%v", S_PHONE );
	filewrite(pffFile,S_CODE,LOGIN);

	close(pffFile);
	execpff(filename(pffFile), stop);

end;


//review or modify cases
function launchReview()
	string pffFilename = pathname(application) + "CPI_CODEBOOK.pff";
	setfile(pffFile,pffFilename,create);

	filewrite(pffFile,"[Run Information]");
	 filewrite(pffFile,"Version=CSPro 7.7");
	filewrite(pffFile,"AppType=Entry");
	
	filewrite(pffFile,"[DataEntryInit]");
	filewrite(pffFile,"ShowInApplicationListing=Hidden");
	
	filewrite(pffFile,"Lock=add");

	filewrite(pffFile,"[Files]");
	filewrite(pffFile,"Application=../CPI_CODEBOOK.ent");
	filewrite(pffFile,"InputData=../Data/CPI_CODEBOOK.csdb|CSPRODB");
	filewrite(pffFile,"Paradata=./CPI_CODEBOOK.cslog");

	filewrite(pffFile,"[ExternalFiles]");

	filewrite(pffFile,"[Parameters]");
	filewrite(pffFile,"OnExit=%s","../Menu/CPI_Menu786.pff");
	filewrite(pffFile,"INTERVIEWER_CODE=%v", S_CODE);
	filewrite(pffFile,S_CODE,LOGIN);

	close(pffFile);
	execpff(filename(pffFile), stop);

end;
//create file backup
function create_backup_file()
	close(STAFF_DICT);
	if ! direxist(temp_path) then 
		dircreate(temp_path);
	endif;
	compress(temp_path+"Data.zip", Pathdata+"*.csdb");
	// compress(temp_path+"Input.zip", PathInput+"*.csdb");
	list string list_file;
	list_file.add(temp_path+"Data.zip");
	compress(temp_path+"backup"+ syslogEnumerator +".zip", list_file);
	open(STAFF_DICT);
end;

//create backup

function backup_local()
	//crée les dossiers de backup 
	create_backup_file();
	//histoirique des backups : download 
	if ! filecopy(temp_path+"backup"+ syslogEnumerator +".zip", pathname(downloads)+"backup" + syslogEnumerator +  timestring("_%Y-%m-%d %H_%M",timestamp()) +".zip") then
		errmsg("Problem to access to the backup folder.");
		exit ;
	endif;
	//Le dossier de restauration 
	if ! filecopy(temp_path+"backup" + syslogEnumerator + ".zip", pathname(downloads)+ "backup" + syslogEnumerator + ".zip") then 
		errmsg("Unable to create a restoration point.");
		exit;
	endif;
	errmsg("Local backup successful.");

	//clé backup sur la clé OTG
	
	string ext_path=pathname(CSEntryExternal);
	if  direxist(ext_path) then 
		if ! filecopy(temp_path+"backup"+ syslogEnumerator +".zip", ext_path+"backup" + syslogEnumerator +  timestring("_%Y-%m-%d %H_%M",timestamp()) +".zip") then
			errmsg("Problem to access to the backup folder.");
			exit;
		endif;
		//Le dossier de restauration 
		if ! filecopy(temp_path+"backup" + syslogEnumerator + ".zip", ext_path+ "backup" + syslogEnumerator + ".zip") then 
			errmsg("Unable to create a restoration point.");
			exit;
		endif;
		errmsg("SD card backup successful.");
	else	
		errmsg("SD card Backup failed.");
	endif;
	errmsg("Local backup successful.");
	exit;
end;


//performance metrix
function CalculateDailyPerformance()
	   // Clear the list (optional, if not automatically empty)
    dailyCounts.clear();
    
    // Calculate counts for today and the previous 6 days
  j = 0;
     while (j <= 6) do
        // Calculate the target date by subtracting i days from today.
        // Assumes dates are in a format that supports simple arithmetic (YYYYMMDD).
        targetDate = sysdate("YYYYMMDD") - j;
        
        // Count the completed cases for the target date.
        countCompleted = countcases(CPI_CODEBOOK_DICT where INTERVIEW_RESULT = 1 );
        
        // Add the count for the day to the dailyCounts list.
        dailyCounts.add(countCompleted);
        
        inc(j);
    enddo;
    
    // Pass the computed dailyCounts to the report engine.
    setreportdata("dailyCounts", dailyCounts);


end;
PROC CPI_MENU786_FF

PROC CPI_MENU786_LEVEL


preproc
	//check first using app 
	if fileexist(PathProg + 'first_using.txt') then
		savesetting(clear);
		filedelete(PathProg + 'first_using.txt');
	endif;
	
	
	datetimesys = edit("99-99-9999",sysdate("DDMMYYYY"));
	
	
	//Utility functions 
	capi_publish_date=edit("9999 - 99 - 99", int(publishdate() / 1000000));
	date = edit("99-99-9999",sysdate("DDMMYYYY"));
	
	userbar(clear);
	userbar(add button,"Update",updateApp());
	userbar(add button,"Local Backup", backup_local());
	userbar(show);
	
	//Set global variables 
	
	PathProg  = pathname(application);
	pathData  = PathProg + "../DATA/";
	PathInput = PathProg + "../INPUTS/"; 
	PathMaps  = PathProg + "../MAPS/"; 
	PathIcons = PathProg + "../ICONS/"; 
	PathResources = PathProg + "../resources/"; 
	Pathreports  = PathProg + "../REPORTS/"; 
	temp_path = PathProg + "../temp/"; 
	
	sysLogSUP=loadsetting("init_tab","no");
	if sysLogSUP="no" then 
		owner_Name = S_NAME;
	else
		owner_Name=S_NAME;
	endif;
	
	
		
	//automatic backup
	numeric last_backup =tonumber(loadsetting("last_auto_backup", "0"));
	if timestamp() - last_backup > 6*3600  then 
		errmsg("Performing Auto-Backup. Please wait until the process is completed.");
		backup_local();
		savesetting("last_auto_backup", timestamp());
	endif;

PROC LOGIN


preproc

// errmsg("You are using the demo version of the application");
// errmsg("Please Use 1234567 for login");
	// Check for saved login
	if loadsetting("login") <> "" then
		LOGIN = loadsetting("login");
		noinput;	
	endif;
	

postproc
// verify that staff code exists in lookup file
if ! loadcase(STAFF_DICT, LOGIN) then
		errmsg("Invalid login. Try again.");
		reenter;
	else
		// Loadcase worked so login code is valid
		// Store login in settings so we can use it on next login
		savesetting("login", maketext("%s",LOGIN));
		
		if S_TYPE = 1 then
			// Interviewer
			skip to MENU_OPTIONS;
		else
			// Supervisor
			skip to SUP_MENU;
		endif;
endif;
PROC MENU_OPTIONS
//entry app
IF $ = 11 THEN
	launchDataEntry();
	//startApp();
ENDIF;

//data review
if $ =16 then 
	launchReview();
endif;

//sync function
if $ = 13 then

		//sync to supervisor 
		//sendToServer();
		//inc(sync_count);
		syncWithSupervisor();
	reenter; 
endif;

//exit
if $ = 14  then
	savesetting("Login", "");
	numeric response = errmsg("Do you want to exit?")
		select("Yes", continue, "No", continue);
	if response = 2 then
		// no
		reenter;
	elseif response = 1 then
		stop(1); // close the program
	endif;

endif;
//dashboard viewer
if $ = 98 then 
ENUMDASHBOARD.VIEW();
reenter;
endif;
//LOGOUT 
if $ = 99 then
	logout();
	reenter;
endif;

//update app
if $ = 15 then
	updateApp();
endif;
PROC SUP_MENU
//data review
if $ =16 then 
	launchReview();
endif;

//dashboard
if $ = 20 then 
//function to view the dashboard
	SUPDASHBOARD.view(); 
	reenter;
endif;


//sync bt to recieve or get data from server
if $ = 17 then
//does nothing for now
	syncWithInterviewer();
	reenter; 
endif;

//sync to server
if $ = 18 then
//does nothing for now
	sendToServer();
	reenter; 
endif;

//exit
if $ = 14  then
	savesetting("Login", "");
	numeric response = errmsg("Do you want to exit?")
		select("Yes", continue, "No", continue);

	if response = 2 then
		// no
		reenter;

	elseif response = 1 then
		stop(1); // close the program
	endif;

endif;

//LOGOUT 
if $ = 99 then
	logout();
	reenter;
endif;

//update app
if $ = 15 then
	updateApp();
endif;
