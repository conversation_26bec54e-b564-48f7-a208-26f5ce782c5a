"""
Data migration processor
"""
import os
import logging
import time
import struct
from enum import Enum, auto
from typing import Dict, List, Optional, Callable, Any, Tuple
from dataclasses import dataclass

from sqlalchemy import Table, Column, MetaData, Integer, String, Date, DECIMAL, text
from sqlalchemy.exc import SQLAlchemyError

from src.core.database.connection import ConnectionManager
from src.core.processors.csdb_processor import CSDBProcessor, CSProDataType

logger = logging.getLogger(__name__)

class MigrationStatus(Enum):
    """Migration status enum"""
    PENDING = auto()
    IN_PROGRESS = auto()
    PAUSED = auto()
    COMPLETED = auto()
    FAILED = auto()
    CANCELLED = auto()

@dataclass
class MigrationProgress:
    """Migration progress information"""
    total_records: int = 0
    processed_records: int = 0
    current_file: str = ""
    current_table: str = ""
    status: MigrationStatus = MigrationStatus.PENDING
    error_message: str = ""
    start_time: float = 0
    end_time: float = 0

    @property
    def percentage(self) -> int:
        """Get progress percentage"""
        if self.total_records == 0:
            return 0
        return int((self.processed_records / self.total_records) * 100)

    @property
    def records_per_second(self) -> float:
        """Get processing speed in records per second"""
        if self.elapsed_time == 0:
            return 0
        return self.processed_records / self.elapsed_time

    @property
    def estimated_time_remaining(self) -> float:
        """Get estimated time remaining in seconds"""
        if self.records_per_second == 0 or self.total_records == 0:
            return 0
        remaining_records = self.total_records - self.processed_records
        return remaining_records / self.records_per_second

    @property
    def elapsed_time(self) -> float:
        """Get elapsed time in seconds"""
        if self.start_time == 0:
            return 0

        end = self.end_time if self.end_time > 0 else time.time()
        return end - self.start_time

class MigrationProcessor:
    """Processor for migrating CSDB data to SQL database"""
    def __init__(
        self,
        connection_manager: ConnectionManager,
        file_paths: List[str],
        table_prefix: str = "",
        batch_size: int = 1000,
        encoding: str = "utf-8"
    ):
        self.connection_manager = connection_manager
        self.file_paths = file_paths
        self.table_prefix = table_prefix
        self.batch_size = batch_size
        self.encoding = encoding
        self.progress = MigrationProgress()
        self.metadata = MetaData()
        self.tables: Dict[str, Table] = {}
        self._cancel_requested = False
        self._pause_requested = False
        self._progress_callback: Optional[Callable[[MigrationProgress], None]] = None

    def set_progress_callback(self, callback: Callable[[MigrationProgress], None]) -> None:
        """Set callback for progress updates"""
        self._progress_callback = callback

    def _update_progress(self) -> None:
        """Update progress and call progress callback"""
        if self._progress_callback:
            self._progress_callback(self.progress)

    def cancel(self) -> None:
        """Request cancellation of migration"""
        self._cancel_requested = True
        logger.info("Migration cancellation requested")

    def pause(self) -> None:
        """Request pause of migration"""
        self._pause_requested = True
        logger.info("Migration pause requested")

    def resume(self) -> None:
        """Resume paused migration"""
        self._pause_requested = False
        logger.info("Migration resumed")

    def migrate(self) -> bool:
        """Migrate CSDB data to SQL database"""
        if not self.connection_manager.engine:
            self.progress.error_message = "Not connected to database"
            self.progress.status = MigrationStatus.FAILED
            self._update_progress()
            logger.error("Not connected to database")
            return False

        # Initialize progress
        self.progress.status = MigrationStatus.IN_PROGRESS
        self.progress.start_time = time.time()
        self.progress.total_records = 0
        self.progress.processed_records = 0
        self._update_progress()

        try:
            # Process each file
            for file_path in self.file_paths:
                if self._cancel_requested:
                    self.progress.status = MigrationStatus.CANCELLED
                    self.progress.end_time = time.time()
                    self._update_progress()
                    logger.info("Migration cancelled")
                    return False

                # Update progress
                self.progress.current_file = os.path.basename(file_path)
                self._update_progress()

                # Process file
                success = self._process_file(file_path)
                if not success:
                    self.progress.status = MigrationStatus.FAILED
                    self.progress.end_time = time.time()
                    self._update_progress()
                    logger.error(f"Failed to process file: {file_path}")
                    return False

            # Complete migration
            self.progress.status = MigrationStatus.COMPLETED
            self.progress.end_time = time.time()
            self._update_progress()
            logger.info("Migration completed successfully")
            return True

        except Exception as e:
            self.progress.error_message = str(e)
            self.progress.status = MigrationStatus.FAILED
            self.progress.end_time = time.time()
            self._update_progress()
            logger.error(f"Migration error: {e}")
            return False

    def _process_file(self, file_path: str) -> bool:
        """Process a single CSDB file"""
        logger.info(f"Processing file: {file_path}")

        # Create processor
        processor = CSDBProcessor(file_path, self.encoding)

        # Validate file
        if not processor.validate():
            self.progress.error_message = f"Invalid CSDB file: {file_path}"
            self._update_progress()
            return False

        # Read header
        if not processor.read_header():
            self.progress.error_message = f"Failed to read CSDB header: {file_path}"
            self._update_progress()
            return False

        # Create tables
        if not self._create_tables(processor):
            self.progress.error_message = f"Failed to create tables for: {file_path}"
            self._update_progress()
            return False

        # Process data
        return self._process_data(processor)

    def _create_tables(self, processor: CSDBProcessor) -> bool:
        """Create database tables for CSDB records"""
        if not processor.dictionary:
            logger.error("Dictionary not loaded")
            return False

        try:
            # Create tables for each record type
            for record in processor.dictionary.records:
                table_name, columns = processor.get_table_schema(record.record_type, self.table_prefix)

                # Skip if table already exists
                if table_name in self.tables:
                    continue

                # Create table
                table_columns = [Column("id", Integer, primary_key=True)]

                # Add columns for each field
                for field in record.fields:
                    if field.occurrences > 1:
                        # Create multiple columns for array fields
                        for i in range(field.occurrences):
                            column_name = f"{field.name}_{i+1}"
                            table_columns.append(self._create_column(column_name, field.data_type, field.length))
                    else:
                        table_columns.append(self._create_column(field.name, field.data_type, field.length))

                # Create table
                table = Table(table_name, self.metadata, *table_columns)
                self.tables[record.record_type] = table

            # Create tables in database
            self.metadata.create_all(self.connection_manager.engine)
            logger.info("Tables created successfully")
            return True

        except SQLAlchemyError as e:
            logger.error(f"Error creating tables: {e}")
            return False

    def _create_column(self, name: str, data_type: CSProDataType, length: int) -> Column:
        """Create SQLAlchemy column based on CSPro data type"""
        if data_type == CSProDataType.NUMERIC:
            if length <= 4:
                return Column(name, Integer)
            elif length <= 9:
                return Column(name, Integer)
            else:
                return Column(name, DECIMAL(length, 0))
        elif data_type == CSProDataType.ALPHA:
            return Column(name, String(length))
        elif data_type == CSProDataType.DATE:
            return Column(name, Date)
        else:
            return Column(name, String(length))

    def _process_data(self, processor: CSDBProcessor) -> bool:
        """Process data from CSDB file"""
        if not processor.dictionary:
            logger.error("Dictionary not loaded")
            return False

        try:
            # Open file for reading
            with open(processor.file_path, "rb") as f:
                # Skip header
                f.seek(4)  # Skip signature
                f.read(4)  # Skip version

                # Skip dictionary
                processor._skip_dictionary(f)

                # Read data section signature
                data_sig = f.read(4)
                if data_sig != b'DATA':
                    logger.error("Invalid data section signature")
                    return False

                # Read record count
                total_records = struct.unpack("<I", f.read(4))[0]
                logger.info(f"Total records: {total_records}")

                # Update progress
                self.progress.total_records += total_records
                self._update_progress()

                # Process records
                record_batches: Dict[str, List[Dict[str, Any]]] = {}

                for _ in range(total_records):
                    # Check for cancellation
                    if self._cancel_requested:
                        logger.info("Processing cancelled")
                        return False

                    # Check for pause
                    while self._pause_requested:
                        self.progress.status = MigrationStatus.PAUSED
                        self._update_progress()
                        time.sleep(0.5)

                    # Reset status if was paused
                    if self.progress.status == MigrationStatus.PAUSED:
                        self.progress.status = MigrationStatus.IN_PROGRESS
                        self._update_progress()

                    # Read record type
                    record_type_len = struct.unpack("<I", f.read(4))[0]
                    record_type = f.read(record_type_len).decode(processor.encoding)

                    # Get record definition
                    record_def = processor.dictionary.get_record(record_type)
                    if not record_def:
                        logger.warning(f"Unknown record type: {record_type}")
                        continue

                    # Update progress
                    self.progress.current_table = f"{self.table_prefix}{record_type}"

                    # Read record data
                    record_data = {}
                    for field in record_def.fields:
                        field_data = processor._read_field_data(f, field)

                        if field.occurrences > 1:
                            # Create multiple columns for array fields
                            for i, value in enumerate(field_data):
                                record_data[f"{field.name}_{i+1}"] = value
                        else:
                            record_data[field.name] = field_data

                    # Add to batch
                    if record_type not in record_batches:
                        record_batches[record_type] = []

                    record_batches[record_type].append(record_data)

                    # Process batch if it reaches batch size
                    if len(record_batches[record_type]) >= self.batch_size:
                        self._insert_batch(record_type, record_batches[record_type])
                        self.progress.processed_records += len(record_batches[record_type])
                        self._update_progress()
                        record_batches[record_type] = []

                # Process remaining batches
                for record_type, batch in record_batches.items():
                    if batch:
                        self._insert_batch(record_type, batch)
                        self.progress.processed_records += len(batch)
                        self._update_progress()

                logger.info(f"Successfully processed data from: {processor.file_path}")
                return True

        except Exception as e:
            logger.error(f"Error processing data: {e}")
            return False

    def _insert_batch(self, record_type: str, batch: List[Dict[str, Any]]) -> None:
        """Insert batch of records into database"""
        if not batch:
            return

        table = self.tables.get(record_type)
        if not table:
            logger.warning(f"Table not found for record type: {record_type}")
            return

        try:
            # Insert batch
            with self.connection_manager.engine.begin() as conn:
                conn.execute(table.insert(), batch)

            logger.debug(f"Inserted {len(batch)} records into {table.name}")

        except SQLAlchemyError as e:
            logger.error(f"Error inserting batch: {e}")
            raise
