﻿
function emailCheck(string email) 
if length(strip(email)) > 0 then
	
// Basic validation for email
if ! regexmatch(email, "^[^@\s]+@[^@\s]+\.[^@\s]+$") then
    errmsg("Please enter a valid Email address");
	reenter;
endif;

endif;

//check if blank
if email = "" then 
	warning("Leaving Field blank, Do you want to continue")
	select("Continue" ,continue, "No", reenter);
endif;

end;

//check Blank Field
function checkBlank(string val) 
	if val = "" then 
		numeric res = errmsg("Leaving Field blank, Do you want to continue")
		select("Continue" ,continue, "No", reenter);
	endif;
	
end;


//checkPhone 
function checkPhone (numeric num)
	if num <> 0 and int(log(num)) <> 6 then
		errmsg("Telephone number must be a 7 digit number! Please enter a correct phone number.");
		reenter;
	endif;
	
end;


//validate name 
function CheckName(String Text)
	if ! regexmatch(Text, "^([A-Za-z ]{2,}[1-9]{0,1})$") then
		errmsg("The name should contain atleast two or more characters.");
		reenter;
		exit false;
	endif;
end;
