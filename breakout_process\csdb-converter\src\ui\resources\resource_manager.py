"""
Resource manager for application resources
"""
import os
import logging
from pathlib import Path
from PyQt6.QtGui import QIcon, QPixmap
from PyQt6.QtCore import QDir

logger = logging.getLogger(__name__)

class ResourceManager:
    """Manager for application resources"""
    
    # Base paths
    _RESOURCE_DIR = os.path.dirname(os.path.abspath(__file__))
    _ICON_DIR = os.path.join(_RESOURCE_DIR, "icons")
    
    @classmethod
    def get_icon(cls, icon_name: str) -> QIcon:
        """Get icon by name"""
        icon_path = os.path.join(cls._ICON_DIR, icon_name)
        icon = QIcon(icon_path)
        
        if icon.isNull():
            logger.warning(f"Icon not found: {icon_name}")
        
        return icon
    
    @classmethod
    def get_pixmap(cls, icon_name: str) -> QPixmap:
        """Get pixmap by name"""
        icon_path = os.path.join(cls._ICON_DIR, icon_name)
        pixmap = QPixmap(icon_path)
        
        if pixmap.isNull():
            logger.warning(f"Pixmap not found: {icon_name}")
        
        return pixmap
    
    @classmethod
    def get_app_icon(cls) -> QIcon:
        """Get application icon"""
        return cls.get_icon("app.png")
    
    @classmethod
    def ensure_resource_dirs(cls) -> None:
        """Ensure resource directories exist"""
        os.makedirs(cls._ICON_DIR, exist_ok=True)
